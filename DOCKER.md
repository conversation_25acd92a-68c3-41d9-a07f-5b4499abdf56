# 🐳 Docker Deployment Guide

This guide explains how to build and run the CBI-E2E Analytics Platform using Docker.

## 📋 Prerequisites

- Docker installed on your system
- Docker Compose (optional, for easier management)

## 🏗️ Building the Docker Image

### Option 1: Using Docker directly

```bash
# Build the image
docker build -t cbi-e2e-analytics .

# Run the container
docker run -d \
  --name cbi-e2e-analytics \
  -p 3000:3000 \
  -p 8080:8080 \
  -v $(pwd)/data:/app/backend/json-test-results \
  cbi-e2e-analytics
```

### Option 2: Using Docker Compose (Recommended)

```bash
# Build and start the services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the services
docker-compose down
```

## 🌐 Accessing the Application

Once the container is running, you can access:

- **Frontend Dashboard**: http://localhost:3000
- **Backend API**: http://localhost:8080/api
- **Health Check**: http://localhost:8080/api/health

## 📁 Data Persistence

The container uses a volume mount to persist test result data:

- **Host Directory**: `./data/` (created automatically)
- **Container Directory**: `/app/backend/json-test-results`

Upload your Playwright JSON test results to this directory or use the web interface.

## 🔧 Environment Variables

You can customize the deployment using environment variables:

```bash
# Using Docker run
docker run -d \
  --name cbi-e2e-analytics \
  -p 3000:3000 \
  -p 8080:8080 \
  -e GIN_MODE=release \
  -e PORT=8080 \
  -e SLACK_WEBHOOK_URL=your_webhook_url \
  -v $(pwd)/data:/app/backend/json-test-results \
  cbi-e2e-analytics
```

### Available Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `GIN_MODE` | `release` | Gin framework mode (debug/release) |
| `PORT` | `8080` | Backend server port |
| `DATA_PATH` | `/app/backend/json-test-results` | Path to test result files |
| `SLACK_WEBHOOK_URL` | - | Slack webhook URL for notifications |
| `SLACK_TOKEN` | - | Slack bot token (alternative to webhook) |
| `SLACK_CHANNEL` | `C091V2CF5DF` | Slack channel ID for notifications |

## 🔍 Troubleshooting

### Container Health Check

```bash
# Check container status
docker ps

# Check health status
docker inspect cbi-e2e-analytics | grep -A 10 Health

# View container logs
docker logs cbi-e2e-analytics
```

### Common Issues

1. **Port conflicts**: Make sure ports 3000 and 8080 are not in use
2. **Permission issues**: Ensure the data directory is writable
3. **Memory issues**: The container needs at least 512MB RAM

### Manual Health Check

```bash
# Test backend health
curl http://localhost:8080/api/health

# Test frontend
curl http://localhost:3000
```

## 🛠️ Development

### Building for Development

```bash
# Build with development settings
docker build -t cbi-e2e-analytics:dev \
  --build-arg GIN_MODE=debug .

# Run with development settings
docker run -d \
  --name cbi-e2e-analytics-dev \
  -p 3000:3000 \
  -p 8080:8080 \
  -e GIN_MODE=debug \
  -v $(pwd)/data:/app/backend/json-test-results \
  cbi-e2e-analytics:dev
```

### Debugging

```bash
# Enter the container
docker exec -it cbi-e2e-analytics sh

# View process list
docker exec cbi-e2e-analytics ps aux

# Check disk usage
docker exec cbi-e2e-analytics df -h
```

## 📊 Performance

The Docker image is optimized for:

- **Size**: ~50MB final image (Alpine-based)
- **Security**: Non-root user execution
- **Performance**: Multi-stage build for minimal footprint
- **Reliability**: Health checks and graceful shutdown

## 🔄 Updates

To update the application:

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

## 📝 Notes

- The container runs both frontend and backend services
- Frontend is served as static files with API proxy
- Data is persisted using volume mounts
- Logs are available via `docker logs` command
- Container supports graceful shutdown with SIGTERM
