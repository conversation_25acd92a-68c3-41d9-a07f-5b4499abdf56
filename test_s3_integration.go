package main

import (
	"fmt"
	"log"
	"strings"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

func main() {
	// Test S3 connection with your credentials
	region := "us-west-2"
	accessKey := "ASIARKYN4QXV3JMO3TQ7"
	secretKey := "d6wiB/Rgo5I5JeHKYGiFCuPvBsgH4CsYyzjvk7ok"
	bucket := "appsulate-jenkins-reports"
	prefix := "workflowtest/dev/ourl-lemon/"

	// Create AWS session
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(region),
		Credentials: credentials.NewStaticCredentials(accessKey, secretKey, ""),
	})
	if err != nil {
		log.Fatalf("Failed to create AWS session: %v", err)
	}

	// Create S3 service client
	svc := s3.New(sess)

	// Test bucket access
	fmt.Printf("Testing S3 connection to bucket: %s\n", bucket)
	fmt.Printf("Looking for objects with prefix: %s\n", prefix)

	// List objects with the prefix
	input := &s3.ListObjectsV2Input{
		Bucket: aws.String(bucket),
		Prefix: aws.String(prefix),
		MaxKeys: aws.Int64(10), // Limit to first 10 objects
	}

	result, err := svc.ListObjectsV2(input)
	if err != nil {
		log.Fatalf("Failed to list objects: %v", err)
	}

	fmt.Printf("Found %d objects:\n", len(result.Contents))
	for i, obj := range result.Contents {
		fmt.Printf("%d. %s (Size: %d bytes, Modified: %s)\n", 
			i+1, *obj.Key, *obj.Size, obj.LastModified.Format("2006-01-02 15:04:05"))
	}

	// Look specifically for run folders
	fmt.Printf("\nLooking for run folders...\n")
	runFolders := make(map[string]bool)
	for _, obj := range result.Contents {
		key := *obj.Key
		if len(key) > len(prefix) {
			relativePath := key[len(prefix):]
			if idx := strings.Index(relativePath, "playwright-report/"); idx > 0 {
				runFolder := prefix + relativePath[:idx]
				runFolders[runFolder] = true
			}
		}
	}

	fmt.Printf("Found %d run folders:\n", len(runFolders))
	i := 1
	for folder := range runFolders {
		fmt.Printf("%d. %s\n", i, folder)
		i++
	}
}
