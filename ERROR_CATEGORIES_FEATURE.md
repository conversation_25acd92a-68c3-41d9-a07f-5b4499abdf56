# Error Categories Feature

## Overview
The Error Categories feature replaces the "Error analysis coming soon" placeholder in the Dashboard with a functional error analysis widget that categorizes test failures and shows the failing tests for each category.

## Features

### Error Categorization
The system automatically categorizes test failures into the following categories:

1. **Timeout** - Tests that fail due to timeout issues
   - Keywords: "timeout", "timed out"
   - Icon: Clock (orange)

2. **Connection** - Network connection related failures
   - Keywords: "connection", "connect", "refused", "unreachable"
   - Icon: Disconnect (red)

3. **Element Not Found** - UI element/selector related failures
   - Keywords: "element", "selector", "locator", "not found", "not visible", "not attached"
   - Icon: Search (blue)

4. **Assertion** - Test assertion/expectation failures
   - Keywords: "assertion", "expect", "should", "failed"
   - Icon: Exclamation (purple)

5. **Network** - HTTP/API related failures
   - Keywords: "network", "http", "request", "response", "fetch"
   - Icon: Global (green)

6. **Navigation** - Page navigation related failures
   - Keywords: "navigation", "navigate", "page", "load"
   - Icon: Compass (cyan)

7. **Permission** - Security/permission related failures
   - Keywords: "permission", "security", "blocked", "denied"
   - Icon: Lock (gold)

8. **Other** - All other uncategorized failures
   - Icon: Question (gray)

### UI Components

#### ErrorCategoriesWidget
- **Location**: `frontend/src/components/widgets/ErrorCategoriesWidget.tsx`
- **Features**:
  - Collapsible categories showing error count
  - List of failed tests for each category (top 10)
  - Test details including fail rate and project name
  - Color-coded icons and tags for each category
  - Responsive design with proper loading and error states

#### Dashboard Integration
- **Location**: `frontend/src/pages/Dashboard.tsx`
- **Changes**:
  - Replaced static "Error analysis coming soon" message
  - Added ErrorCategoriesWidget component
  - Passes current filters to the widget for consistent filtering

### Backend Implementation

#### New API Endpoint
- **Endpoint**: `GET /api/analytics/error-categories`
- **Handler**: `GetErrorCategoriesWithTests` in `analytics_handler.go`
- **Response**: Array of `ErrorCategoryDetail` objects

#### Data Processing
- **Service**: `GetErrorCategoriesWithTests` in `analytics_service.go`
- **Logic**:
  1. Gets all test analytics with failure rates > 0
  2. Categorizes error messages using improved categorization logic
  3. Groups failed tests by error category
  4. Sorts categories by count (descending)
  5. Sorts tests within each category by fail rate (descending)

#### Type Definitions
- **Backend**: `ErrorCategoryDetail` in `valueobjects/analytics.go`
- **Frontend**: `ErrorCategoryDetail` in `types/index.ts`

## Usage

### For Users
1. Navigate to the Dashboard page
2. The Error Categories section will automatically load and display:
   - Categories with error counts as collapsible panels
   - Click on any category to expand and see the failing tests
   - Each test shows its name, file path, fail rate, and project

### For Developers
The error categorization logic can be extended by modifying the `categorizeError` function in `backend/domain/services/analytics_service.go`.

## Technical Details

### API Flow
1. Frontend calls `getErrorCategoriesWithTests()` from `api.ts`
2. Backend processes test results and extracts error messages
3. Error messages are categorized using keyword matching
4. Failed tests are grouped by category and returned
5. Frontend renders the collapsible widget with the data

### Performance Considerations
- Only tests with failure rates > 0 are processed
- Top 10 tests per category are shown to limit UI clutter
- Data is filtered based on current dashboard filters
- Efficient sorting and grouping algorithms are used

### Error Handling
- Loading states during data fetch
- Error alerts for API failures
- Graceful handling of empty data (shows "No errors found!")
- Fallback to "Other" category for unrecognized error types

## Future Enhancements
1. Add more sophisticated error pattern matching
2. Allow custom error category definitions
3. Add drill-down navigation to test details
4. Include error trend analysis over time
5. Add export functionality for error reports
