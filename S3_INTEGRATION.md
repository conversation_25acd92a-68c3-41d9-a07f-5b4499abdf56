# 🚀 S3 Integration Guide

This guide explains how to set up and use S3 integration for video storage and automated data ingestion in the Test Analytics Platform.

## 📋 **Overview**

The S3 integration provides two main features:

1. **Video Storage**: Store and display test execution videos from S3
2. **Data Source**: Automatically ingest test results from S3 buckets

## 🔧 **Setup Instructions**

### **1. AWS Prerequisites**

#### **Create S3 Bucket**
```bash
# Create bucket for test results
aws s3 mb s3://your-test-results-bucket

# Create folder structure
aws s3api put-object --bucket your-test-results-bucket --key test-results/
aws s3api put-object --bucket your-test-results-bucket --key test-videos/
```

#### **Create IAM User with S3 Permissions**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "s3:GetObject",
        "s3:PutObject",
        "s3:DeleteObject",
        "s3:ListBucket"
      ],
      "Resource": [
        "arn:aws:s3:::your-test-results-bucket",
        "arn:aws:s3:::your-test-results-bucket/*"
      ]
    }
  ]
}
```

### **2. Environment Configuration**

#### **Backend Environment Variables**
Add these to your `.env` file or environment:

```bash
# Enable S3 Integration
ENABLE_S3_INTEGRATION=true

# AWS Configuration
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_S3_BUCKET=your-test-results-bucket

# S3 Prefixes (optional)
AWS_S3_VIDEO_PREFIX=test-videos/
AWS_S3_DATA_PREFIX=test-results/

# Sync Settings (optional)
S3_SYNC_INTERVAL_MINUTES=5
```

#### **Docker Compose Configuration**
```yaml
environment:
  - ENABLE_S3_INTEGRATION=true
  - AWS_REGION=us-east-1
  - AWS_ACCESS_KEY_ID=your_access_key
  - AWS_SECRET_ACCESS_KEY=your_secret_key
  - AWS_S3_BUCKET=your-test-results-bucket
  - AWS_S3_VIDEO_PREFIX=test-videos/
  - AWS_S3_DATA_PREFIX=test-results/
  - S3_SYNC_INTERVAL_MINUTES=5
```

### **3. Playwright Configuration for Video Upload**

#### **Update Playwright Config**
```javascript
// playwright.config.js
module.exports = {
  use: {
    // Enable video recording
    video: 'retain-on-failure',
    // or 'on' for all tests
  },
  
  // Configure test results
  reporter: [
    ['json', { outputFile: 'test-results.json' }]
  ],
};
```

#### **Upload Script Example**
```bash
#!/bin/bash
# upload-results.sh

# Upload test results JSON
aws s3 cp test-results.json s3://your-test-results-bucket/test-results/

# Upload videos
aws s3 sync test-results/ s3://your-test-results-bucket/test-videos/ \
  --include "*.webm" --include "*.mp4"
```

## 🎯 **Features**

### **1. Video Playback**
- **Location**: Test Cases page → Video column
- **Features**: 
  - In-browser video player
  - Download functionality
  - Automatic presigned URL generation
  - 24-hour URL expiration

### **2. Screenshot Gallery**
- **Location**: Test Cases page → Screenshots column
- **Features**:
  - Thumbnail grid view
  - Full-size preview
  - Multiple screenshots per test

### **3. Automated Data Ingestion**
- **Frequency**: Configurable (default: 5 minutes)
- **Process**: 
  1. Scans S3 bucket for new JSON files
  2. Downloads and processes test results
  3. Updates analytics automatically
  4. Tracks processed files to avoid duplicates

### **4. S3 Management Interface**
- **Location**: Sidebar → S3 Integration
- **Features**:
  - Connection status monitoring
  - Manual sync trigger
  - Cache management
  - Configuration guidance

## 📊 **API Endpoints**

### **Video Endpoints**
```bash
# Get video URL
GET /api/v1/videos/{s3Key}

# Get S3 status
GET /api/v1/videos/status
```

### **S3 Management Endpoints**
```bash
# Get S3 status
GET /api/v1/s3/status

# Trigger manual sync
POST /api/v1/s3/sync

# Clear processed files cache
POST /api/v1/s3/clear-cache
```

## 🔍 **Monitoring & Troubleshooting**

### **Check S3 Status**
1. Navigate to **S3 Integration** page
2. View connection status and metrics
3. Check processed files count

### **Common Issues**

#### **Connection Failed**
```bash
# Check AWS credentials
aws sts get-caller-identity

# Test bucket access
aws s3 ls s3://your-test-results-bucket/
```

#### **Videos Not Loading**
- Verify S3 bucket permissions
- Check video file paths in test results
- Ensure presigned URLs are not expired

#### **Data Not Syncing**
- Check S3 sync logs in application
- Verify JSON file format
- Ensure files are in correct S3 prefix

### **Logs**
```bash
# Backend logs show S3 operations
docker logs cbi-e2e-analytics

# Look for:
# "S3 integration enabled, initializing..."
# "S3 sync completed. Success: X, Errors: Y"
# "S3 sync error: ..."
```

## 🚀 **Best Practices**

### **1. File Organization**
```
s3://your-bucket/
├── test-results/
│   ├── 2024-01-15/
│   │   ├── test-run-001.json
│   │   └── test-run-002.json
│   └── 2024-01-16/
└── test-videos/
    ├── 2024-01-15/
    │   ├── test-001-video.mp4
    │   └── test-002-video.mp4
    └── 2024-01-16/
```

### **2. Security**
- Use IAM roles instead of access keys in production
- Implement bucket policies for additional security
- Enable S3 server-side encryption
- Use VPC endpoints for private access

### **3. Cost Optimization**
- Set up S3 lifecycle policies
- Use S3 Intelligent Tiering
- Monitor data transfer costs
- Consider S3 Standard-IA for older files

### **4. Performance**
- Use CloudFront for video delivery
- Implement parallel uploads
- Optimize video file sizes
- Cache presigned URLs appropriately

## 🔄 **Migration Guide**

### **From File-based to S3**
1. **Backup existing data**
2. **Upload historical files to S3**
3. **Enable S3 integration**
4. **Verify data integrity**
5. **Update CI/CD pipelines**

### **Rollback Plan**
1. **Disable S3 integration**: Set `ENABLE_S3_INTEGRATION=false`
2. **Restart application**
3. **Resume file-based uploads**

## 📞 **Support**

For issues or questions:
1. Check application logs
2. Verify AWS permissions
3. Test S3 connectivity
4. Review configuration settings

The S3 integration is designed to be backward compatible - existing functionality continues to work even when S3 is enabled.
