#!/bin/bash

# Build script for CBI-E2E Analytics Platform Docker image
# This script builds and optionally runs the Docker container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
IMAGE_NAME="cbi-e2e-analytics"
CONTAINER_NAME="cbi-e2e-analytics"
FRONTEND_PORT="3000"
BACKEND_PORT="8080"
DATA_DIR="./data"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Print banner
print_banner() {
    echo "
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║              CBI-E2E Analytics Platform                       ║
║                                                               ║
║              🐳 Docker Build & Deploy Script                 ║
║              🎭 Playwright Test Results Analytics            ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker."
        exit 1
    fi
    
    log_info "✅ Docker is available and running"
}

# Create data directory
create_data_dir() {
    if [ ! -d "$DATA_DIR" ]; then
        log_info "Creating data directory: $DATA_DIR"
        mkdir -p "$DATA_DIR"
    else
        log_info "Data directory already exists: $DATA_DIR"
    fi
}

# Stop and remove existing container
cleanup_existing() {
    if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        log_info "Stopping and removing existing container: $CONTAINER_NAME"
        docker stop "$CONTAINER_NAME" 2>/dev/null || true
        docker rm "$CONTAINER_NAME" 2>/dev/null || true
    fi
}

# Build Docker image
build_image() {
    log_info "Building Docker image: $IMAGE_NAME"
    log_info "This may take a few minutes..."
    
    if docker build -t "$IMAGE_NAME" .; then
        log_info "✅ Docker image built successfully"
    else
        log_error "❌ Failed to build Docker image"
        exit 1
    fi
}

# Run container
run_container() {
    log_info "Starting container: $CONTAINER_NAME"
    
    docker run -d \
        --name "$CONTAINER_NAME" \
        -p "$FRONTEND_PORT:3000" \
        -p "$BACKEND_PORT:8080" \
        -v "$(pwd)/$DATA_DIR:/app/backend/json-test-results" \
        -e GIN_MODE=release \
        --restart unless-stopped \
        "$IMAGE_NAME"
    
    if [ $? -eq 0 ]; then
        log_info "✅ Container started successfully"
    else
        log_error "❌ Failed to start container"
        exit 1
    fi
}

# Wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for backend
    for i in {1..30}; do
        if curl -f "http://localhost:$BACKEND_PORT/api/health" &>/dev/null; then
            log_info "✅ Backend is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            log_warn "⚠️  Backend health check timeout, but continuing..."
        fi
        sleep 2
    done
    
    # Wait for frontend
    for i in {1..30}; do
        if curl -f "http://localhost:$FRONTEND_PORT" &>/dev/null; then
            log_info "✅ Frontend is ready"
            break
        fi
        if [ $i -eq 30 ]; then
            log_warn "⚠️  Frontend health check timeout, but continuing..."
        fi
        sleep 2
    done
}

# Show status
show_status() {
    log_info "🚀 CBI-E2E Analytics Platform is running!"
    echo ""
    log_info "📊 Frontend Dashboard: http://localhost:$FRONTEND_PORT"
    log_info "🔧 Backend API: http://localhost:$BACKEND_PORT/api"
    log_info "❤️  Health Check: http://localhost:$BACKEND_PORT/api/health"
    log_info "📁 Data Directory: $DATA_DIR"
    echo ""
    log_info "Container Management:"
    log_info "  - View logs: docker logs $CONTAINER_NAME"
    log_info "  - Stop: docker stop $CONTAINER_NAME"
    log_info "  - Start: docker start $CONTAINER_NAME"
    log_info "  - Remove: docker rm $CONTAINER_NAME"
}

# Main execution
main() {
    print_banner
    
    # Parse command line arguments
    BUILD_ONLY=false
    SKIP_RUN=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            --skip-run)
                SKIP_RUN=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --build-only    Only build the image, don't run container"
                echo "  --skip-run      Build image and prepare, but don't run container"
                echo "  -h, --help      Show this help message"
                echo ""
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Execute steps
    check_prerequisites
    create_data_dir
    
    if [ "$BUILD_ONLY" = false ]; then
        cleanup_existing
    fi
    
    build_image
    
    if [ "$BUILD_ONLY" = false ] && [ "$SKIP_RUN" = false ]; then
        run_container
        wait_for_services
        show_status
    elif [ "$BUILD_ONLY" = true ]; then
        log_info "✅ Build completed. Image: $IMAGE_NAME"
        log_info "To run: docker run -d --name $CONTAINER_NAME -p $FRONTEND_PORT:3000 -p $BACKEND_PORT:8080 -v \$(pwd)/$DATA_DIR:/app/backend/json-test-results $IMAGE_NAME"
    else
        log_info "✅ Build and preparation completed. Ready to run."
        log_info "To run: docker run -d --name $CONTAINER_NAME -p $FRONTEND_PORT:3000 -p $BACKEND_PORT:8080 -v \$(pwd)/$DATA_DIR:/app/backend/json-test-results $IMAGE_NAME"
    fi
}

# Run main function
main "$@"
