#!/bin/bash

# Test script to verify AWS credentials are working in Docker container

echo "🔍 Testing AWS credentials in Docker container..."

# Check if container is running
if ! docker ps | grep -q cbi-e2e-analytics; then
    echo "❌ Container is not running. Start it first with: ./run-docker-with-aws.sh"
    exit 1
fi

echo ""
echo "📋 Environment variables in container:"
docker exec cbi-e2e-analytics env | grep -E "AWS_|S3_|ENABLE_S3" | sort

echo ""
echo "🔧 Testing S3 connection via API:"
curl -s http://localhost:8080/api/v1/s3/status | jq '.' 2>/dev/null || curl -s http://localhost:8080/api/v1/s3/status

echo ""
echo "📊 S3 Configuration page: http://localhost:3000/s3-config"
