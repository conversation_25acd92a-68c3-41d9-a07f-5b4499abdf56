version: "3.8"

services:
  cbi-e2e-analytics:
    image: 091834058219.dkr.ecr.eu-central-1.amazonaws.com/cbi-e2e-analytics-v1.0.0:latest
    container_name: cbi-e2e-analytics
    ports:
      - "3000:3000" # Frontend
      - "8080:8080" # Backend API
    env_file:
      - backend/.env
    environment:
      # Only pass AWS credentials as environment variables (override .env if provided)
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
    volumes:
      # Mount the .env file to ensure it's available inside the container
      - ./backend/.env:/app/backend/.env:ro
      # Mount a local directory for persistent test result data
      - ./data:/app/backend/json-test-results
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - cbi-analytics-network

networks:
  cbi-analytics-network:
    driver: bridge
