package mcp

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"cbi-e2e-analytics/config"
	"cbi-e2e-analytics/domain/entities"
)

// MCPService handles Model Context Protocol integration
type MCPService struct {
	config     *config.Config
	httpClient *http.Client
}

// NewMCPService creates a new MCP service
func NewMCPService(cfg *config.Config) *MCPService {
	return &MCPService{
		config: cfg,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// MCPRequest represents a request to the MCP service
type MCPRequest struct {
	Model    string                 `json:"model"`
	Messages []MCPMessage           `json:"messages"`
	Context  map[string]interface{} `json:"context,omitempty"`
	Tools    []MCPTool              `json:"tools,omitempty"`
}

// MCPMessage represents a message in the conversation
type MCPMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// MCPTool represents available tools for the model
type MCPTool struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// MCPResponse represents the response from MCP service
type MCPResponse struct {
	Choices []MCPChoice `json:"choices"`
	Usage   MCPUsage    `json:"usage"`
}

// MCPChoice represents a response choice
type MCPChoice struct {
	Message      MCPMessage `json:"message"`
	FinishReason string     `json:"finish_reason"`
}

// MCPUsage represents token usage information
type MCPUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ProcessQuery sends a query to the MCP service with test data context
func (m *MCPService) ProcessQuery(ctx context.Context, query string, testResults []entities.TestResult) (string, error) {
	// Build context from test data
	contextData := m.buildTestDataContext(testResults)
	
	// Create system prompt with test data context
	systemPrompt := m.buildSystemPrompt(contextData)
	
	// Prepare MCP request
	request := MCPRequest{
		Model: "gpt-4", // or your preferred model
		Messages: []MCPMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: query,
			},
		},
		Context: map[string]interface{}{
			"testData": contextData,
		},
		Tools: m.getAvailableTools(),
	}

	// Send request to MCP service
	response, err := m.sendMCPRequest(ctx, request)
	if err != nil {
		return "", fmt.Errorf("failed to send MCP request: %w", err)
	}

	if len(response.Choices) == 0 {
		return "No response generated", nil
	}

	return response.Choices[0].Message.Content, nil
}

// buildTestDataContext creates a structured context from test results
func (m *MCPService) buildTestDataContext(testResults []entities.TestResult) map[string]interface{} {
	context := map[string]interface{}{
		"totalFiles": len(testResults),
		"summary":    map[string]interface{}{},
		"projects":   map[string]interface{}{},
		"trends":     []interface{}{},
	}

	totalTests := 0
	passedTests := 0
	failedTests := 0
	skippedTests := 0
	totalDuration := 0.0
	projectStats := make(map[string]map[string]interface{})

	for _, result := range testResults {
		for _, suite := range result.Suites {
			projectName := suite.Title
			if projectStats[projectName] == nil {
				projectStats[projectName] = map[string]interface{}{
					"tests":    0,
					"passed":   0,
					"failed":   0,
					"skipped":  0,
					"duration": 0.0,
				}
			}

			for _, spec := range suite.Specs {
				for _, test := range spec.Tests {
					totalTests++
					projectStats[projectName]["tests"] = projectStats[projectName]["tests"].(int) + 1

					for _, testResult := range test.Results {
						totalDuration += float64(testResult.Duration)
						projectStats[projectName]["duration"] = projectStats[projectName]["duration"].(float64) + float64(testResult.Duration)

						switch testResult.Status {
						case "passed", "expected":
							passedTests++
							projectStats[projectName]["passed"] = projectStats[projectName]["passed"].(int) + 1
						case "failed", "unexpected", "timeout":
							failedTests++
							projectStats[projectName]["failed"] = projectStats[projectName]["failed"].(int) + 1
						case "skipped":
							skippedTests++
							projectStats[projectName]["skipped"] = projectStats[projectName]["skipped"].(int) + 1
						}
					}
				}
			}
		}
	}

	context["summary"] = map[string]interface{}{
		"totalTests":      totalTests,
		"passedTests":     passedTests,
		"failedTests":     failedTests,
		"skippedTests":    skippedTests,
		"totalDuration":   totalDuration,
		"averageDuration": totalDuration / float64(totalTests),
		"passRate":        float64(passedTests) / float64(totalTests) * 100,
		"failRate":        float64(failedTests) / float64(totalTests) * 100,
	}

	context["projects"] = projectStats

	return context
}

// buildSystemPrompt creates a system prompt with test data context
func (m *MCPService) buildSystemPrompt(contextData map[string]interface{}) string {
	return fmt.Sprintf(`You are an AI assistant specialized in analyzing test automation results. You have access to comprehensive test data and can provide insights, answer questions, and help users understand their test suite performance.

**Available Test Data Context:**
%s

**Your Capabilities:**
- Analyze test performance and trends
- Identify failing tests and error patterns
- Compare project performance
- Provide recommendations for test optimization
- Answer specific questions about test cases
- Generate insights from test data

**Response Guidelines:**
- Provide clear, actionable insights
- Use data from the context to support your answers
- Format responses with markdown for better readability
- Include specific numbers and percentages when relevant
- Suggest follow-up questions or actions when appropriate

Please analyze the user's query and provide a helpful response based on the available test data.`, 
		m.formatContextForPrompt(contextData))
}

// formatContextForPrompt formats context data for inclusion in the system prompt
func (m *MCPService) formatContextForPrompt(contextData map[string]interface{}) string {
	summary := contextData["summary"].(map[string]interface{})

	return fmt.Sprintf(`
- Total Test Files: %d
- Total Tests: %.0f
- Pass Rate: %.1f%%
- Fail Rate: %.1f%%
- Average Duration: %.2f seconds
- Projects: %d active projects`,
		contextData["totalFiles"].(int),
		summary["totalTests"].(int),
		summary["passRate"].(float64),
		summary["failRate"].(float64),
		summary["averageDuration"].(float64)/1000,
		len(contextData["projects"].(map[string]map[string]interface{})))
}

// getAvailableTools returns the tools available to the MCP model
func (m *MCPService) getAvailableTools() []MCPTool {
	return []MCPTool{
		{
			Name:        "analyze_test_failures",
			Description: "Analyze failing tests and identify patterns",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"project": map[string]interface{}{
						"type":        "string",
						"description": "Project name to analyze",
					},
				},
			},
		},
		{
			Name:        "compare_projects",
			Description: "Compare performance between different projects",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"projects": map[string]interface{}{
						"type":        "array",
						"description": "List of project names to compare",
					},
				},
			},
		},
		{
			Name:        "get_performance_insights",
			Description: "Get insights about test performance and duration",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"threshold": map[string]interface{}{
						"type":        "number",
						"description": "Duration threshold in seconds",
					},
				},
			},
		},
	}
}

// sendMCPRequest sends a request to the MCP service
func (m *MCPService) sendMCPRequest(ctx context.Context, request MCPRequest) (*MCPResponse, error) {
	// For now, return a mock response since MCP integration would require
	// actual MCP service endpoint configuration
	
	log.Printf("MCP Request: %+v", request)
	
	// Mock response - in real implementation, this would call the actual MCP service
	mockResponse := &MCPResponse{
		Choices: []MCPChoice{
			{
				Message: MCPMessage{
					Role:    "assistant",
					Content: m.generateMockResponse(request.Messages[len(request.Messages)-1].Content, request.Context),
				},
				FinishReason: "stop",
			},
		},
		Usage: MCPUsage{
			PromptTokens:     100,
			CompletionTokens: 200,
			TotalTokens:      300,
		},
	}

	return mockResponse, nil
}

// generateMockResponse generates a mock response for testing
func (m *MCPService) generateMockResponse(query string, context map[string]interface{}) string {
	testData := context["testData"].(map[string]interface{})
	summary := testData["summary"].(map[string]interface{})
	
	return fmt.Sprintf(`Based on your test data analysis:

**Test Suite Overview:**
- Total Tests: %.0f
- Pass Rate: %.1f%%
- Fail Rate: %.1f%%
- Average Duration: %.2f seconds

**Key Insights:**
- Your test suite has a %s pass rate
- %s performance based on average duration
- %d projects are being tested

**Recommendations:**
- Focus on improving failing tests (%.1f%% fail rate)
- Consider optimizing slow tests if duration > 10 seconds
- Monitor trends over time for continuous improvement

*This is a mock response. In production, this would be powered by a real MCP service with advanced AI capabilities.*`,
		summary["totalTests"].(int),
		summary["passRate"].(float64),
		summary["failRate"].(float64),
		summary["averageDuration"].(float64)/1000,
		func() string {
			if summary["passRate"].(float64) > 90 {
				return "excellent"
			} else if summary["passRate"].(float64) > 80 {
				return "good"
			}
			return "needs improvement"
		}(),
		func() string {
			avgDuration := summary["averageDuration"].(float64) / 1000
			if avgDuration < 5 {
				return "excellent"
			} else if avgDuration < 15 {
				return "good"
			}
			return "slow"
		}(),
		len(testData["projects"].(map[string]map[string]interface{})),
		summary["failRate"].(float64))
}
