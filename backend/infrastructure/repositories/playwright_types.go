package repositories

import "time"

// PlaywrightTestResult represents the raw Playwright JSON structure
type PlaywrightTestResult struct {
	Config PlaywrightConfig `json:"config"`
	Suites []PlaywrightSuite `json:"suites"`
	Errors []string          `json:"errors"`
	Stats  PlaywrightStats   `json:"stats"`

	// Fields for individual test execution results
	WorkerIndex   int                      `json:"workerIndex"`
	ParallelIndex int                      `json:"parallelIndex"`
	Status        string                   `json:"status"`
	Duration      int                      `json:"duration"`
	TestErrors    []PlaywrightError        `json:"errors"`
	Stdout        []PlaywrightOutput       `json:"stdout"`
	Stderr        []PlaywrightOutput       `json:"stderr"`
	Retry         int                      `json:"retry"`
	StartTime     time.Time                `json:"startTime"`
	Annotations   []PlaywrightAnnotation   `json:"annotations"`
	Attachments   []PlaywrightAttachment   `json:"attachments"`
	Steps         []PlaywrightStep         `json:"steps"`
	Error         *PlaywrightError         `json:"error,omitempty"`
	ErrorLocation *PlaywrightLocation      `json:"errorLocation,omitempty"`
}

// PlaywrightConfig represents Playwright configuration
type PlaywrightConfig struct {
	ConfigFile    string              `json:"configFile"`
	RootDir       string              `json:"rootDir"`
	Version       string              `json:"version"`
	Workers       int                 `json:"workers"`
	Projects      []PlaywrightProject `json:"projects"`
	ForbidOnly    bool                `json:"forbidOnly"`
	FullyParallel bool                `json:"fullyParallel"`
}

// PlaywrightProject represents a Playwright project
type PlaywrightProject struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	TestDir   string `json:"testDir"`
	Timeout   int    `json:"timeout"`
	Retries   int    `json:"retries"`
	OutputDir string `json:"outputDir"`
}

// PlaywrightSuite represents a test suite
type PlaywrightSuite struct {
	Title  string             `json:"title"`
	File   string             `json:"file"`
	Column int                `json:"column"`
	Line   int                `json:"line"`
	Specs  []PlaywrightSpec   `json:"specs"`
	Suites []PlaywrightSuite  `json:"suites"`
}

// PlaywrightSpec represents a test specification
type PlaywrightSpec struct {
	Title string            `json:"title"`
	OK    bool              `json:"ok"`
	Tags  []string          `json:"tags"`
	Tests []PlaywrightTest  `json:"tests"`
	ID    string            `json:"id"`
	File  string            `json:"file"`
	Line  int               `json:"line"`
	Column int              `json:"column"`
}

// PlaywrightTest represents an individual test
type PlaywrightTest struct {
	Timeout        int                      `json:"timeout"`
	Annotations    []PlaywrightAnnotation   `json:"annotations"`
	ExpectedStatus string                   `json:"expectedStatus"`
	ProjectID      string                   `json:"projectId"`
	ProjectName    string                   `json:"projectName"`
	Results        []PlaywrightTestResult   `json:"results"`
	Status         string                   `json:"status"`
}

// PlaywrightAnnotation represents test annotations
type PlaywrightAnnotation struct {
	Type        string `json:"type"`
	Description string `json:"description"`
}

// PlaywrightError represents an error
type PlaywrightError struct {
	Message  string               `json:"message"`
	Stack    string               `json:"stack"`
	Location *PlaywrightLocation  `json:"location,omitempty"`
	Snippet  string               `json:"snippet,omitempty"`
}

// PlaywrightOutput represents stdout/stderr output
type PlaywrightOutput struct {
	Text string `json:"text"`
}

// PlaywrightAttachment represents test attachments
type PlaywrightAttachment struct {
	Name        string `json:"name"`
	ContentType string `json:"contentType"`
	Path        string `json:"path"`
}

// PlaywrightStep represents a test step
type PlaywrightStep struct {
	Title    string            `json:"title"`
	Duration int               `json:"duration"`
	Error    *PlaywrightError  `json:"error,omitempty"`
}

// PlaywrightLocation represents a file location
type PlaywrightLocation struct {
	File   string `json:"file"`
	Line   int    `json:"line"`
	Column int    `json:"column"`
}

// PlaywrightStats represents test execution statistics
type PlaywrightStats struct {
	StartTime  time.Time `json:"startTime"`
	Duration   float64   `json:"duration"`
	Expected   int       `json:"expected"`
	Skipped    int       `json:"skipped"`
	Unexpected int       `json:"unexpected"`
	Flaky      int       `json:"flaky"`
}
