package repositories

import (
	"cbi-e2e-analytics/domain/entities"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
)

// TestResultRepository handles persistence of test results
type TestResultRepository struct {
	dataPath string
}

// NewTestResultRepository creates a new test result repository
func NewTestResultRepository(dataPath string) *TestResultRepository {
	return &TestResultRepository{
		dataPath: dataPath,
	}
}

// LoadAllTestResults loads all test results from JSON files
func (r *TestResultRepository) LoadAllTestResults() ([]entities.TestResult, error) {
	var results []entities.TestResult

	files, err := os.ReadDir(r.dataPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %w", r.dataPath, err)
	}

	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".json") {
			continue
		}

		filePath := filepath.Join(r.dataPath, file.Name())
		result, err := r.loadTestResultFromFile(filePath, file.Name())
		if err != nil {
			fmt.Printf("Warning: failed to load file %s: %v\n", file.Name(), err)
			continue
		}

		results = append(results, *result)
	}

	return results, nil
}

// SaveTestResult saves a test result to a JSON file
func (r *TestResultRepository) SaveTestResult(result *entities.TestResult, fileName string) error {
	if fileName == "" {
		fileName = fmt.Sprintf("results_%s.json", time.Now().Format("20060102_150405"))
	}

	filePath := filepath.Join(r.dataPath, fileName)
	
	data, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal test result: %w", err)
	}

	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %s: %w", filePath, err)
	}

	return nil
}

// DeleteTestResult deletes a test result file
func (r *TestResultRepository) DeleteTestResult(fileName string) error {
	filePath := filepath.Join(r.dataPath, fileName)
	
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file %s does not exist", fileName)
	}

	err := os.Remove(filePath)
	if err != nil {
		return fmt.Errorf("failed to delete file %s: %w", fileName, err)
	}

	return nil
}

// GetTestResultFiles returns a list of available test result files
func (r *TestResultRepository) GetTestResultFiles() ([]string, error) {
	var files []string

	entries, err := os.ReadDir(r.dataPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read directory %s: %w", r.dataPath, err)
	}

	for _, entry := range entries {
		if !entry.IsDir() && strings.HasSuffix(entry.Name(), ".json") {
			files = append(files, entry.Name())
		}
	}

	return files, nil
}

// loadTestResultFromFile loads a test result from a specific file
func (r *TestResultRepository) loadTestResultFromFile(filePath, fileName string) (*entities.TestResult, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	// First try to unmarshal as our domain entity (for files saved with RunFolderName)
	var domainResult entities.TestResult
	err = json.Unmarshal(data, &domainResult)
	if err == nil && domainResult.RunFolderName != "" {
		// This is already in our domain format with RunFolderName preserved
		domainResult.FileName = fileName
		domainResult.LoadedAt = time.Now()
		return &domainResult, nil
	}

	// Fallback to Playwright format conversion
	var playwrightResult PlaywrightTestResult
	err = json.Unmarshal(data, &playwrightResult)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON from %s: %w", filePath, err)
	}

	// Convert Playwright format to our domain entities
	result := r.convertPlaywrightResult(&playwrightResult, fileName)
	return result, nil
}

// convertPlaywrightResult converts Playwright JSON format to our domain entities
func (r *TestResultRepository) convertPlaywrightResult(pr *PlaywrightTestResult, fileName string) *entities.TestResult {
	result := &entities.TestResult{
		ID:       uuid.New().String(),
		FileName: fileName,
		LoadedAt: time.Now(),
		Config: entities.Config{
			ConfigFile:    pr.Config.ConfigFile,
			RootDir:       pr.Config.RootDir,
			Version:       pr.Config.Version,
			Workers:       pr.Config.Workers,
			ForbidOnly:    pr.Config.ForbidOnly,
			FullyParallel: pr.Config.FullyParallel,
		},
		Stats: entities.Stats{
			StartTime:  pr.Stats.StartTime,
			Duration:   pr.Stats.Duration,
			Expected:   pr.Stats.Expected,
			Skipped:    pr.Stats.Skipped,
			Unexpected: pr.Stats.Unexpected,
			Flaky:      pr.Stats.Flaky,
		},
		Errors: pr.Errors,
	}

	// Convert projects
	for _, project := range pr.Config.Projects {
		result.Config.Projects = append(result.Config.Projects, entities.Project{
			ID:        project.ID,
			Name:      project.Name,
			TestDir:   project.TestDir,
			Timeout:   project.Timeout,
			Retries:   project.Retries,
			OutputDir: project.OutputDir,
		})
	}

	// Convert suites
	for _, suite := range pr.Suites {
		result.Suites = append(result.Suites, r.convertSuite(suite))
	}

	return result
}

// convertSuite converts a Playwright suite to our domain entity
func (r *TestResultRepository) convertSuite(ps PlaywrightSuite) entities.Suite {
	suite := entities.Suite{
		Title:  ps.Title,
		File:   ps.File,
		Column: ps.Column,
		Line:   ps.Line,
	}

	// Convert specs
	for _, spec := range ps.Specs {
		suite.Specs = append(suite.Specs, r.convertSpec(spec))
	}

	// Convert nested suites
	for _, subSuite := range ps.Suites {
		suite.Suites = append(suite.Suites, r.convertSuite(subSuite))
	}

	return suite
}

// convertSpec converts a Playwright spec to our domain entity
func (r *TestResultRepository) convertSpec(ps PlaywrightSpec) entities.Spec {
	spec := entities.Spec{
		Title:  ps.Title,
		OK:     ps.OK,
		Tags:   ps.Tags,
		ID:     ps.ID,
		File:   ps.File,
		Line:   ps.Line,
		Column: ps.Column,
	}

	// Convert tests
	for _, test := range ps.Tests {
		spec.Tests = append(spec.Tests, r.convertTest(test))
	}

	return spec
}

// convertTest converts a Playwright test to our domain entity
func (r *TestResultRepository) convertTest(pt PlaywrightTest) entities.Test {
	test := entities.Test{
		Timeout:        pt.Timeout,
		ExpectedStatus: pt.ExpectedStatus,
		ProjectID:      pt.ProjectID,
		ProjectName:    pt.ProjectName,
		Status:         pt.Status,
	}

	// Convert annotations
	for _, annotation := range pt.Annotations {
		test.Annotations = append(test.Annotations, entities.Annotation{
			Type:        annotation.Type,
			Description: annotation.Description,
		})
	}

	// Convert results
	for _, result := range pt.Results {
		test.Results = append(test.Results, r.convertTestExecution(result, pt.ProjectName))
	}

	return test
}

// convertTestExecution converts a Playwright test result to our domain entity
func (r *TestResultRepository) convertTestExecution(pr PlaywrightTestResult, projectName string) entities.TestExecution {
	execution := entities.TestExecution{
		WorkerIndex:   pr.WorkerIndex,
		ParallelIndex: pr.ParallelIndex,
		Status:        pr.Status,
		Duration:      pr.Duration,
		Retry:         pr.Retry,
		StartTime:     pr.StartTime,
		ProjectName:   projectName,
	}

	// Convert annotations
	for _, annotation := range pr.Annotations {
		execution.Annotations = append(execution.Annotations, entities.Annotation{
			Type:        annotation.Type,
			Description: annotation.Description,
		})
	}

	// Convert errors
	for _, err := range pr.TestErrors {
		testError := entities.TestError{
			Message: err.Message,
			Stack:   err.Stack,
		}
		if err.Location != nil {
			testError.Location = &entities.Location{
				File:   err.Location.File,
				Line:   err.Location.Line,
				Column: err.Location.Column,
			}
		}
		execution.Errors = append(execution.Errors, testError)
	}

	// Convert error if present
	if pr.Error != nil {
		execution.Error = &entities.TestError{
			Message: pr.Error.Message,
			Stack:   pr.Error.Stack,
			Snippet: pr.Error.Snippet,
		}
		if pr.Error.Location != nil {
			execution.Error.Location = &entities.Location{
				File:   pr.Error.Location.File,
				Line:   pr.Error.Location.Line,
				Column: pr.Error.Location.Column,
			}
		}
	}

	// Convert stdout
	for _, stdout := range pr.Stdout {
		execution.Stdout = append(execution.Stdout, entities.Output{
			Text: stdout.Text,
		})
	}

	// Convert stderr
	for _, stderr := range pr.Stderr {
		execution.Stderr = append(execution.Stderr, entities.Output{
			Text: stderr.Text,
		})
	}

	// Convert attachments
	for _, attachment := range pr.Attachments {
		execution.Attachments = append(execution.Attachments, entities.Attachment{
			Name:        attachment.Name,
			ContentType: attachment.ContentType,
			Path:        attachment.Path,
		})
	}

	// Convert steps
	for _, step := range pr.Steps {
		execStep := entities.Step{
			Title:    step.Title,
			Duration: step.Duration,
		}
		if step.Error != nil {
			execStep.Error = &entities.TestError{
				Message: step.Error.Message,
				Stack:   step.Error.Stack,
			}
		}
		execution.Steps = append(execution.Steps, execStep)
	}

	return execution
}
