package aws

import (
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"

	"cbi-e2e-analytics/application/usecases"
	"cbi-e2e-analytics/domain/entities"
)

type S3DataSource struct {
	s3Service      *S3Service
	analyticsUseCase *usecases.AnalyticsUseCase
	dataPrefix     string
	videoPrefix    string
	maxFiles       int
	processedFiles map[string]bool
	mu             sync.RWMutex
	syncDays       int
}

func NewS3DataSource(s3Service *S3Service, analyticsUseCase *usecases.AnalyticsUseCase, dataPrefix, videoPrefix string, maxFiles, syncDays int) *S3DataSource {
	return &S3DataSource{
		s3Service:        s3Service,
		analyticsUseCase: analyticsUseCase,
		dataPrefix:       dataPrefix,
		videoPrefix:      videoPrefix,
		processedFiles:   make(map[string]bool),
		maxFiles:         maxFiles,
		mu:               sync.RWMutex{},
		syncDays:         syncDays,
	}
}

func (s *S3DataSource) ScanForNewFiles() ([]string, error) {
	// Get the last N run folders from the bucket (configurable)
	runFolders, err := s.getLastNRunFolders()
	if err != nil {
		return nil, err
	}

	log.Printf("Found %d nightly run folders to scan", len(runFolders))
	for i, folder := range runFolders {
		log.Printf("Nightly run folder %d: %s", i+1, folder)
	}

	var newFiles []string
	for _, runFolder := range runFolders {
		// Look for JSON files in playwright-report/json-results/ directories
		jsonPath := runFolder + "playwright-report/json-results/"
		log.Printf("Scanning path: %s", jsonPath)

		// Use limited listing since we typically expect only a few JSON files per run folder
		files, err := s.s3Service.ListObjectsWithLimit(jsonPath, 50) // Limit to 50 objects per folder
		if err != nil {
			log.Printf("Error listing objects in %s: %v", jsonPath, err)
			continue // Skip this folder if we can't list its contents
		}

		log.Printf("Found %d files in %s", len(files), jsonPath)
		for _, file := range files {
			log.Printf("  File: %s", file)
			if strings.HasSuffix(strings.ToLower(file), ".json") {
				// Check if we've already processed this file
				if !s.processedFiles[file] {
					log.Printf("  -> Adding new JSON file: %s", file)
					newFiles = append(newFiles, file)
				} else {
					log.Printf("  -> Already processed: %s", file)
				}
			}
		}
	}

	return newFiles, nil
}

// getLastNRunFolders gets nightly run folders from the last 5 days only (current date to 5 days back)
// This ensures we only process recent nightly test runs and avoid thousands of old files
func (s *S3DataSource) getLastNRunFolders() ([]string, error) {
	log.Printf("Finding nightly run folders (pw-originalurl-nightly) from last %d days with prefix: %s", s.syncDays, s.dataPrefix)

	// Always use date-based search for the last 5 days only, filtering for nightly runs
	// This prevents processing old data and focuses on recent nightly test runs
	log.Printf("Using date-based search for last %d days to get recent nightly runs only", s.syncDays)
	return s.getLastNRunFoldersFromObjects()
}

// getLastNRunFoldersFromObjects uses date-based search for nightly runs from the last 5 days only
func (s *S3DataSource) getLastNRunFoldersFromObjects() ([]string, error) {
	log.Printf("Using date-based search for last %d days to find nightly run folders", s.syncDays)

	// Always use date-based search for the last 5 days only, filtering for nightly runs
	// No fallback to object listing to avoid processing thousands of old files
	runFolders, err := s.findRecentRunFoldersByDate()
	if err != nil {
		log.Printf("Date-based search failed: %v", err)
		return nil, err
	}

	log.Printf("Date-based search found %d nightly run folders from last %d days", len(runFolders), s.syncDays)
	return runFolders, nil
}

// findRecentRunFoldersByDate searches for nightly run folders from the last 5 days only
func (s *S3DataSource) findRecentRunFoldersByDate() ([]string, error) {
	runFolderMap := make(map[string]bool)

	// Generate date prefixes for the last 5 days only (current date to 5 days back)
	datePrefixes := s.generateRecentDatePrefixes(s.syncDays) // Use configurable days

	log.Printf("Searching for nightly run folders (pw-originalurl-nightly) from last %d days using %d date prefixes", s.syncDays, len(datePrefixes))

	for _, datePrefix := range datePrefixes {
		searchPrefix := s.dataPrefix + datePrefix
		log.Printf("Searching with prefix: %s", searchPrefix)

		// Use folder listing for each date prefix (much more efficient)
		folders, err := s.s3Service.ListFolders(searchPrefix, "/", 100) // Increased limit per date
		if err != nil {
			log.Printf("Failed to list folders for prefix %s: %v", searchPrefix, err)
			continue
		}

		log.Printf("Found %d folders for date prefix %s", len(folders), datePrefix)

		// Add valid run folders that contain 'pw-originalurl-nightly' in the name
		nightlyCount := 0
		totalCount := 0
		for _, folder := range folders {
			relativePath := strings.TrimPrefix(folder, s.dataPrefix)
			relativePath = strings.TrimSuffix(relativePath, "/")

			if s.isRunFolder(relativePath) {
				totalCount++
				if s.isNightlyRunFolder(relativePath) {
					log.Printf("  ✅ Adding nightly folder: %s", relativePath)
					runFolderMap[folder] = true
					nightlyCount++
				} else {
					log.Printf("  ❌ Skipping non-nightly folder: %s", relativePath)
				}
			}
		}
		log.Printf("Found %d nightly run folders out of %d total run folders for date prefix %s", nightlyCount, totalCount, datePrefix)
	}

	// Convert to slice and sort by timestamp
	var runFolders []string
	for folder := range runFolderMap {
		runFolders = append(runFolders, folder)
	}

	// Sort by timestamp (most recent first)
	sort.Slice(runFolders, func(i, j int) bool {
		timestampI := s.extractTimestampFromFolder(runFolders[i])
		timestampJ := s.extractTimestampFromFolder(runFolders[j])
		return timestampI > timestampJ
	})

	log.Printf("Date-based search found %d nightly run folders from last %d days", len(runFolders), s.syncDays)
	return runFolders, nil
}

// findRunFoldersWithLimitedListing method removed - we only use date-based search for last 5 days
// This prevents processing thousands of old files and ensures we only get recent data

// generateRecentDatePrefixes generates date prefixes for the last N days from current date
func (s *S3DataSource) generateRecentDatePrefixes(days int) []string {
	var prefixes []string

	// Get current date and work backwards
	now := time.Now().UTC()
	log.Printf("Current date (UTC): %s", now.Format("2006-01-02"))

	for i := 0; i < days; i++ {
		date := now.AddDate(0, 0, -i)
		// Format as YYYY-MM-DD to match the folder naming pattern
		datePrefix := date.Format("2006-01-02")
		prefixes = append(prefixes, datePrefix)
	}

	log.Printf("Generated date prefixes for last %d days: %v", days, prefixes)
	return prefixes
}

// isRunFolder checks if a folder path looks like a run folder based on timestamp pattern
func (s *S3DataSource) isRunFolder(folderPath string) bool {
	// Run folder format: 2025-07-14T16:22:41Z-pw-e2e-test-workflow-template-...
	// Look for timestamp pattern at the beginning
	if len(folderPath) < 20 {
		return false
	}

	// Check for ISO timestamp pattern: YYYY-MM-DDTHH:MM:SSZ
	timestampPattern := folderPath[:20]
	if len(timestampPattern) >= 19 &&
		timestampPattern[4] == '-' &&
		timestampPattern[7] == '-' &&
		timestampPattern[10] == 'T' &&
		timestampPattern[13] == ':' &&
		timestampPattern[16] == ':' &&
		(len(timestampPattern) == 19 || timestampPattern[19] == 'Z') {
		return true
	}

	return false
}

// isNightlyRunFolder checks if a folder path contains 'pw-originalurl-nightly' in the name
func (s *S3DataSource) isNightlyRunFolder(folderPath string) bool {
	isNightly := strings.Contains(folderPath, "pw-originalurl-nightly")
	log.Printf("    Checking if '%s' is nightly: %v", folderPath, isNightly)
	return isNightly
}

// extractTimestampFromFolder extracts the timestamp from a run folder path
// Folder format: workflowtest/dev/ourl-lemon/2025-07-14T16:22:41Z-pw-e2e-test-workflow-template-9dj66-e2e-test-749182521/
// We want to extract: 2025-07-14T16:22:41Z
func (s *S3DataSource) extractTimestampFromFolder(folderPath string) string {
	// Remove the prefix to get just the folder name
	relativePath := strings.TrimPrefix(folderPath, s.dataPrefix)

	// Remove trailing slash if present
	relativePath = strings.TrimSuffix(relativePath, "/")

	// The timestamp is at the beginning of the folder name
	// Format: 2025-07-14T16:22:41Z-pw-e2e-test-workflow-template-...
	// Find the first occurrence of "-pw-" to extract just the timestamp part
	if idx := strings.Index(relativePath, "-pw-"); idx > 0 {
		return relativePath[:idx]
	}

	// Fallback: return the whole relative path if pattern not found
	return relativePath
}

func (s *S3DataSource) ProcessS3Files() error {
	return s.processS3FilesWithContext("automatic")
}

// ProcessS3FilesManual is specifically for manual sync triggers
func (s *S3DataSource) ProcessS3FilesManual() error {
	return s.processS3FilesWithContext("manual")
}

// processS3FilesWithContext handles both manual and automatic syncs with context logging
func (s *S3DataSource) processS3FilesWithContext(syncType string) error {
	log.Printf("Starting S3 file scan (%s sync)...", syncType)
	log.Printf("Current processed files cache size: %d", len(s.processedFiles))
	log.Printf("Max files configured: %d", s.maxFiles)

	newFiles, err := s.ScanForNewFiles()
	if err != nil {
		return fmt.Errorf("failed to scan for new files: %w", err)
	}

	if len(newFiles) == 0 {
		log.Printf("No new files found in S3 (%s sync)", syncType)
		return nil
	}

	log.Printf("Found %d new files to process (%s sync)", len(newFiles), syncType)

	successCount := 0
	errorCount := 0

	for _, file := range newFiles {
		err := s.processFile(file)
		if err != nil {
			log.Printf("Error processing file %s: %v", file, err)
			errorCount++
		} else {
			log.Printf("Successfully processed file: %s", file)
			s.processedFiles[file] = true
			successCount++
		}
	}

	log.Printf("S3 sync completed (%s). Success: %d, Errors: %d", syncType, successCount, errorCount)
	log.Printf("Total processed files in cache: %d", len(s.processedFiles))
	return nil
}

func (s *S3DataSource) processFile(key string) error {
	// Download file content
	data, err := s.s3Service.GetObject(key)
	if err != nil {
		return fmt.Errorf("failed to download file: %w", err)
	}

	// Parse JSON
	var testResult entities.TestResult
	if err := json.Unmarshal(data, &testResult); err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Generate a unique filename that includes project ID to avoid overwrites
	// Extract timestamp and project ID to create a unique filename
	timestamp := s.extractTimestampFromFolder(key)
	projectID := s.extractProjectIDFromTestResult(&testResult)

	var simpleFilename string
	if projectID != "" {
		simpleFilename = fmt.Sprintf("%s-%s-test-results.json", timestamp, projectID)
	} else {
		simpleFilename = fmt.Sprintf("%s-test-results.json", timestamp)
	}

	log.Printf("Processing S3 key: %s", key)
	log.Printf("Extracted project ID: %s", projectID)
	log.Printf("Using simple filename for storage: %s", simpleFilename)

	// Extract run folder name from S3 key for video/screenshot mapping
	// Example: workflowtest/dev/ourl-lemon/2025-07-16T06:11:04Z-pw-originalurl-nightly-1752604200-89hhm-e2e-test-2612665655/playwright-report/json-results/test-results.json
	// Extract: 2025-07-16T06:11:04Z-pw-originalurl-nightly-1752604200-89hhm-e2e-test-2612665655
	runFolderName := s.extractRunFolderFromS3Key(key)
	testResult.RunFolderName = runFolderName

	// Process the test result using simple filename for storage
	err = s.analyticsUseCase.ProcessTestResult(&testResult, simpleFilename)
	if err != nil {
		return fmt.Errorf("failed to process test result: %w", err)
	}

	return nil
}

// extractRunFolderFromS3Key extracts the run folder name from an S3 key
// Example: workflowtest/dev/ourl-lemon/2025-07-16T06:11:04Z-pw-originalurl-nightly-1752604200-89hhm-e2e-test-2612665655/playwright-report/json-results/test-results.json
// Returns: 2025-07-16T06:11:04Z-pw-originalurl-nightly-1752604200-89hhm-e2e-test-2612665655
func (s *S3DataSource) extractRunFolderFromS3Key(s3Key string) string {
	// Split by "/" and find the run folder (should be the 4th segment after workflowtest/dev/ourl-lemon/)
	parts := strings.Split(s3Key, "/")
	if len(parts) >= 4 {
		// parts[0] = "workflowtest", parts[1] = "dev", parts[2] = "ourl-lemon", parts[3] = run folder
		return parts[3]
	}

	// Fallback: return empty string if we can't extract the run folder
	return ""
}

// extractProjectIDFromTestResult extracts the project name from config.projects
// It finds the project where testDir is "/e2e/tests" and ignores coverage projects
func (s *S3DataSource) extractProjectIDFromTestResult(testResult *entities.TestResult) string {
	// Look through config.projects to find the main test project
	for _, project := range testResult.Config.Projects {
		// Skip coverage projects
		if project.ID == "coverage-setup" || project.ID == "coverage-teardown" {
			continue
		}

		// Find the project with testDir "/e2e/tests"
		if project.TestDir == "/e2e/tests" {
			// Return the project name (not ID)
			return project.Name
		}
	}

	// Fallback: return empty string if no matching project found
	return ""
}

func (s *S3DataSource) GetProcessedFileCount() int {
	return len(s.processedFiles)
}

func (s *S3DataSource) ClearProcessedCache() {
	s.mu.Lock()
	defer s.mu.Unlock()

	log.Printf("Clearing processed files cache (had %d entries)", len(s.processedFiles))
	s.processedFiles = make(map[string]bool)
	log.Println("Processed files cache cleared - will reprocess latest nightly runs only")
}

// GetMaxFiles returns the configured maximum number of files to process
func (s *S3DataSource) GetMaxFiles() int {
	return s.maxFiles
}

// SetMaxFiles updates the maximum number of files to process
func (s *S3DataSource) SetMaxFiles(maxFiles int) {
	log.Printf("Updating max files from %d to %d", s.maxFiles, maxFiles)
	s.maxFiles = maxFiles
}
