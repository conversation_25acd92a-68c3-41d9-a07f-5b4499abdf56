package aws

import (
	"context"
	"fmt"
	"io"
	"log"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type S3Service struct {
	client *s3.Client
	bucket string
}

type S3Config struct {
	Region          string
	AccessKeyID     string
	SecretAccessKey string
	SessionToken    string
	Bucket          string
}

func NewS3Service(cfg S3Config) (*S3Service, error) {
	var awsCfg aws.Config
	var err error

	if cfg.AccessKeyID != "" && cfg.SecretAccessKey != "" {
		// Use provided credentials (with optional session token for temporary credentials)
		awsCfg, err = config.LoadDefaultConfig(context.TODO(),
			config.WithRegion(cfg.Region),
			config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(
				cfg.AccessKeyID,
				cfg.SecretAccessKey,
				cfg.SessionToken,
			)),
		)
	} else {
		// Use default credential chain (IAM roles, etc.)
		awsCfg, err = config.LoadDefaultConfig(context.TODO(),
			config.WithRegion(cfg.Region),
		)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to load AWS config: %w", err)
	}

	return &S3Service{
		client: s3.NewFromConfig(awsCfg),
		bucket: cfg.Bucket,
	}, nil
}

func (s *S3Service) GeneratePresignedURL(key string, expiration time.Duration) (string, error) {
	presignClient := s3.NewPresignClient(s.client)

	request, err := presignClient.PresignGetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(s.bucket),
		Key:    aws.String(key),
	}, func(opts *s3.PresignOptions) {
		opts.Expires = expiration
	})

	if err != nil {
		return "", fmt.Errorf("failed to generate presigned URL: %w", err)
	}

	return request.URL, nil
}

func (s *S3Service) ListObjects(prefix string) ([]string, error) {
	var keys []string
	var continuationToken *string

	log.Printf("Starting to list objects with prefix: %s", prefix)

	for {
		input := &s3.ListObjectsV2Input{
			Bucket: aws.String(s.bucket),
			Prefix: aws.String(prefix),
		}

		if continuationToken != nil {
			input.ContinuationToken = continuationToken
			log.Printf("Using continuation token for next batch")
		}

		result, err := s.client.ListObjectsV2(context.TODO(), input)
		if err != nil {
			log.Printf("Error listing objects: %v", err)
			return nil, fmt.Errorf("failed to list objects: %w", err)
		}

		log.Printf("Retrieved %d objects in this batch", len(result.Contents))

		for _, obj := range result.Contents {
			if obj.Key != nil {
				keys = append(keys, *obj.Key)
			}
		}

		// Check if there are more objects to fetch
		if result.IsTruncated {
			continuationToken = result.NextContinuationToken
			log.Printf("More objects available, continuing...")
		} else {
			log.Printf("All objects retrieved. Total: %d", len(keys))
			break
		}
	}

	return keys, nil
}

// ListObjectsWithLimit lists objects with a specified maximum number of results
// This is more efficient when you only need a limited number of objects
func (s *S3Service) ListObjectsWithLimit(prefix string, maxKeys int) ([]string, error) {
	var keys []string
	var continuationToken *string
	totalRetrieved := 0

	log.Printf("Starting to list objects with prefix: %s (max: %d)", prefix, maxKeys)

	for totalRetrieved < maxKeys {
		remainingKeys := maxKeys - totalRetrieved
		batchSize := remainingKeys
		if batchSize > 1000 {
			batchSize = 1000 // AWS S3 max limit per request
		}

		input := &s3.ListObjectsV2Input{
			Bucket:  aws.String(s.bucket),
			Prefix:  aws.String(prefix),
			MaxKeys: int32(batchSize),
		}

		if continuationToken != nil {
			input.ContinuationToken = continuationToken
		}

		result, err := s.client.ListObjectsV2(context.TODO(), input)
		if err != nil {
			log.Printf("Error listing objects: %v", err)
			return nil, fmt.Errorf("failed to list objects: %w", err)
		}

		batchCount := len(result.Contents)
		log.Printf("Retrieved %d objects in this batch", batchCount)

		for _, obj := range result.Contents {
			if obj.Key != nil {
				keys = append(keys, *obj.Key)
				totalRetrieved++
				if totalRetrieved >= maxKeys {
					break
				}
			}
		}

		// Check if there are more objects to fetch and we haven't reached our limit
		if result.IsTruncated && totalRetrieved < maxKeys {
			continuationToken = result.NextContinuationToken
		} else {
			break
		}
	}

	log.Printf("Limited object listing completed. Retrieved: %d (requested max: %d)", len(keys), maxKeys)
	return keys, nil
}

// ListFolders lists "folders" (common prefixes) under a given prefix
// This is more efficient for finding folder structures in S3
func (s *S3Service) ListFolders(prefix string, delimiter string, maxKeys int) ([]string, error) {
	var folders []string
	var continuationToken *string
	totalRetrieved := 0

	log.Printf("Starting to list folders with prefix: %s, delimiter: %s (max: %d)", prefix, delimiter, maxKeys)

	for totalRetrieved < maxKeys {
		remainingKeys := maxKeys - totalRetrieved
		batchSize := remainingKeys
		if batchSize > 1000 {
			batchSize = 1000 // AWS S3 max limit per request
		}

		input := &s3.ListObjectsV2Input{
			Bucket:    aws.String(s.bucket),
			Prefix:    aws.String(prefix),
			Delimiter: aws.String(delimiter),
			MaxKeys:   int32(batchSize),
		}

		if continuationToken != nil {
			input.ContinuationToken = continuationToken
		}

		result, err := s.client.ListObjectsV2(context.TODO(), input)
		if err != nil {
			log.Printf("Error listing folders: %v", err)
			return nil, fmt.Errorf("failed to list folders: %w", err)
		}

		batchCount := len(result.CommonPrefixes)
		log.Printf("Retrieved %d folders in this batch", batchCount)

		for _, commonPrefix := range result.CommonPrefixes {
			if commonPrefix.Prefix != nil {
				folders = append(folders, *commonPrefix.Prefix)
				totalRetrieved++
				if totalRetrieved >= maxKeys {
					break
				}
			}
		}

		// Check if there are more folders to fetch and we haven't reached our limit
		if result.IsTruncated && totalRetrieved < maxKeys {
			continuationToken = result.NextContinuationToken
		} else {
			break
		}
	}

	log.Printf("Folder listing completed. Retrieved: %d folders (requested max: %d)", len(folders), maxKeys)
	return folders, nil
}

func (s *S3Service) GetObject(key string) ([]byte, error) {
	result, err := s.client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(s.bucket),
		Key:    aws.String(key),
	})

	if err != nil {
		return nil, fmt.Errorf("failed to get object: %w", err)
	}
	defer result.Body.Close()

	data, err := io.ReadAll(result.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read object body: %w", err)
	}

	return data, nil
}

func (s *S3Service) ObjectExists(key string) (bool, error) {
	_, err := s.client.HeadObject(context.TODO(), &s3.HeadObjectInput{
		Bucket: aws.String(s.bucket),
		Key:    aws.String(key),
	})

	if err != nil {
		// Check if it's a "not found" error
		if strings.Contains(err.Error(), "NotFound") || strings.Contains(err.Error(), "404") {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

func (s *S3Service) GetBucketName() string {
	return s.bucket
}
