package scheduler

import (
	"log"
	"time"

	"cbi-e2e-analytics/infrastructure/aws"
)

type S3Scheduler struct {
	dataSource *aws.S3DataSource
	interval   time.Duration
	stopChan   chan bool
	running    bool
}

func NewS3Scheduler(dataSource *aws.S3DataSource, intervalMinutes int) *S3Scheduler {
	return &S3Scheduler{
		dataSource: dataSource,
		interval:   time.Duration(intervalMinutes) * time.Minute,
		stopChan:   make(chan bool),
		running:    false,
	}
}

func (s *S3Scheduler) Start() {
	if s.running {
		log.Println("S3 scheduler is already running")
		return
	}

	s.running = true
	log.Printf("Starting S3 scheduler with %v interval", s.interval)

	// Run immediately on start
	go func() {
		if err := s.dataSource.ProcessS3Files(); err != nil {
			log.Printf("Initial S3 sync error: %v", err)
		}
	}()

	// Start periodic sync
	ticker := time.NewTicker(s.interval)
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				if err := s.dataSource.ProcessS3Files(); err != nil {
					log.Printf("S3 sync error: %v", err)
				}
			case <-s.stopChan:
				log.Println("S3 scheduler stopped")
				return
			}
		}
	}()
}

func (s *S3Scheduler) Stop() {
	if !s.running {
		return
	}

	s.running = false
	close(s.stopChan)
	log.Println("Stopping S3 scheduler...")
}

func (s *S3Scheduler) IsRunning() bool {
	return s.running
}
