package handlers

import (
	"log"
	"net/http"
	"strconv"

	"cbi-e2e-analytics/infrastructure/aws"
	"cbi-e2e-analytics/infrastructure/scheduler"

	"github.com/gin-gonic/gin"
)

type S3Handler struct {
	s3Service *aws.S3Service
	dataSource *aws.S3DataSource
	scheduler  *scheduler.S3Scheduler
}

func NewS3Handler(s3Service *aws.S3Service, dataSource *aws.S3DataSource, scheduler *scheduler.S3Scheduler) *S3Handler {
	return &S3Handler{
		s3Service:  s3Service,
		dataSource: dataSource,
		scheduler:  scheduler,
	}
}

func (h *S3Handler) GetS3Status(c *gin.Context) {
	if h.s3Service == nil {
		c.JSON(http.StatusOK, gin.H{
			"s3Enabled":        false,
			"message":          "S3 integration not configured",
			"schedulerRunning": false,
			"processedFiles":   0,
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"s3Enabled":        true,
		"bucket":           h.s3Service.GetBucketName(),
		"schedulerRunning": h.scheduler != nil && h.scheduler.IsRunning(),
		"processedFiles":   h.getProcessedFileCount(),
		"maxFiles":         h.getMaxFiles(),
	})
}

func (h *S3Handler) TriggerSync(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	// Clear cache before sync to ensure we get the latest runs
	h.dataSource.ClearProcessedCache()

	go func() {
		if err := h.dataSource.ProcessS3FilesManual(); err != nil {
			log.Printf("Manual S3 sync error: %v", err)
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"message": "Manual S3 sync triggered (processing nightly runs from last 5 days)",
	})
}

func (h *S3Handler) ClearCache(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	h.dataSource.ClearProcessedCache()

	c.JSON(http.StatusOK, gin.H{
		"message": "Processed files cache cleared",
	})
}

func (h *S3Handler) getProcessedFileCount() int {
	if h.dataSource == nil {
		return 0
	}
	return h.dataSource.GetProcessedFileCount()
}

func (h *S3Handler) getMaxFiles() int {
	if h.dataSource == nil {
		return 0
	}
	return h.dataSource.GetMaxFiles()
}

// GetMaxFiles returns the current max files configuration
func (h *S3Handler) GetMaxFiles(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"maxFiles": h.dataSource.GetMaxFiles(),
	})
}

// SetMaxFiles updates the max files configuration
func (h *S3Handler) SetMaxFiles(c *gin.Context) {
	if h.dataSource == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "S3 data source not configured",
		})
		return
	}

	maxFilesStr := c.Param("maxFiles")
	maxFiles, err := strconv.Atoi(maxFilesStr)
	if err != nil || maxFiles < 1 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid maxFiles value. Must be a positive integer.",
		})
		return
	}

	h.dataSource.SetMaxFiles(maxFiles)

	c.JSON(http.StatusOK, gin.H{
		"message":  "Max files updated successfully",
		"maxFiles": maxFiles,
	})
}
