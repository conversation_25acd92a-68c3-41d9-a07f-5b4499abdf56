package handlers

import (
	"cbi-e2e-analytics/application/usecases"
	"cbi-e2e-analytics/domain/valueobjects"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// AnalyticsHandler handles HTTP requests for analytics
type Analytics<PERSON>and<PERSON> struct {
	analyticsUseCase *usecases.AnalyticsUseCase
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsUseCase *usecases.AnalyticsUseCase) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsUseCase: analyticsUseCase,
	}
}

// GetDashboardMetrics handles GET /api/analytics/dashboard
func (h *AnalyticsHandler) GetDashboardMetrics(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	metrics, err := h.analyticsUseCase.GetDashboardMetrics(filters)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetTestAnalytics handles GET /api/analytics/tests
func (h *AnalyticsHandler) GetTestAnalytics(c *gin.Context) {
	fmt.Printf("🔍 GetTestAnalytics called with query params: %v\n", c.Request.URL.RawQuery)
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	fmt.Printf("🔍 Parsed filters: %+v\n", filters)



	analytics, err := h.analyticsUseCase.GetTestAnalytics(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	fmt.Printf("🔍 Returning %d tests after filtering\n", len(analytics))



	c.JSON(http.StatusOK, analytics)
}

// GetErrorCategoriesWithTests handles GET /api/analytics/error-categories
func (h *AnalyticsHandler) GetErrorCategoriesWithTests(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	errorCategories, err := h.analyticsUseCase.GetErrorCategoriesWithTests(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, errorCategories)
}

// GetTestRuns handles GET /api/analytics/test-runs
func (h *AnalyticsHandler) GetTestRuns(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	testRuns, err := h.analyticsUseCase.GetTestRuns(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, testRuns)
}

// GetChartData handles GET /api/analytics/charts/:type
func (h *AnalyticsHandler) GetChartData(c *gin.Context) {
	chartType := c.Param("type")
	
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	chartData, err := h.analyticsUseCase.GetChartData(chartType, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, chartData)
}

// GetWordCloudData handles GET /api/analytics/wordcloud
func (h *AnalyticsHandler) GetWordCloudData(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	wordCloudData, err := h.analyticsUseCase.GetWordCloudData(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, wordCloudData)
}

// GetSunburstData handles GET /api/analytics/sunburst
func (h *AnalyticsHandler) GetSunburstData(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	sunburstData, err := h.analyticsUseCase.GetSunburstData(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, sunburstData)
}

// GetBubbleChartData handles GET /api/analytics/bubble
func (h *AnalyticsHandler) GetBubbleChartData(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	bubbleData, err := h.analyticsUseCase.GetBubbleChartData(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, bubbleData)
}

// GetHeatmapData handles GET /api/analytics/heatmap
func (h *AnalyticsHandler) GetHeatmapData(c *gin.Context) {
	filters, err := h.parseFilters(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	heatmapData, err := h.analyticsUseCase.GetHeatmapData(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, heatmapData)
}

// parseFilters parses query parameters into filter criteria
func (h *AnalyticsHandler) parseFilters(c *gin.Context) (*valueobjects.FilterCriteria, error) {
	filters := &valueobjects.FilterCriteria{}

	// Parse date filters
	if startDateStr := c.Query("startDate"); startDateStr != "" {
		startDate, err := time.Parse("2006-01-02", startDateStr)
		if err != nil {
			return nil, err
		}
		filters.StartDate = &startDate
	}

	if endDateStr := c.Query("endDate"); endDateStr != "" {
		endDate, err := time.Parse("2006-01-02", endDateStr)
		if err != nil {
			return nil, err
		}
		// Set endDate to end of day (23:59:59.999) to include all tests from that day
		endOfDay := endDate.Add(23*time.Hour + 59*time.Minute + 59*time.Second + 999*time.Millisecond)
		filters.EndDate = &endOfDay
	}

	// Parse array filters
	if status := c.QueryArray("status"); len(status) > 0 {
		filters.Status = status
	} else {
		// Also check for single value
		if singleStatus := c.Query("status"); singleStatus != "" {
			filters.Status = []string{singleStatus}
		}
	}

	if projectName := c.QueryArray("projectName"); len(projectName) > 0 {
		fmt.Printf("🔍 Handler: Parsed projectName array: %v\n", projectName)
		filters.ProjectName = projectName
	} else {
		// Also check for single value
		if singleProject := c.Query("projectName"); singleProject != "" {
			filters.ProjectName = []string{singleProject}
		}
	}

	if filePath := c.QueryArray("filePath"); len(filePath) > 0 {
		filters.FilePath = filePath
	} else {
		// Also check for single value
		if singleFilePath := c.Query("filePath"); singleFilePath != "" {
			filters.FilePath = []string{singleFilePath}
		}
	}

	if tags := c.QueryArray("tags"); len(tags) > 0 {
		filters.Tags = tags
	} else {
		// Also check for single value
		if singleTag := c.Query("tags"); singleTag != "" {
			filters.Tags = []string{singleTag}
		}
	}

	return filters, nil
}

// GetFilterOptions handles GET /api/analytics/filter-options
func (h *AnalyticsHandler) GetFilterOptions(c *gin.Context) {
	// Get dynamic filter options based on actual test data
	options, err := h.analyticsUseCase.GetFilterOptions()
	if err != nil {
		// Fallback to static options if there's an error
		options = map[string]any{
			"status":      []string{"passed", "failed", "skipped", "flaky"},
			"projectName": []string{}, // Empty fallback, will be populated from actual data
			"priority":    []string{"P1", "P2", "P3", "P4"},
			"tags":        []string{"clipboard", "persist-banner", "welcome-banner"},
		}
	}

	c.JSON(http.StatusOK, options)
}

// GetAvailableDates handles GET /api/analytics/available-dates
func (h *AnalyticsHandler) GetAvailableDates(c *gin.Context) {
	dates, err := h.analyticsUseCase.GetAvailableDates()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"dates": dates})
}

// Health check endpoint
func (h *AnalyticsHandler) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "healthy",
		"service": "CBI-E2E Analytics API",
		"version": "1.0.0",
	})
}
