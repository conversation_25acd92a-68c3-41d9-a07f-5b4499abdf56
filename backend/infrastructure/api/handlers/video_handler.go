package handlers

import (
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"

	"cbi-e2e-analytics/infrastructure/aws"

	"github.com/gin-gonic/gin"
)

type VideoHandler struct {
	s3Service *aws.S3Service
}

func NewVideoHandler(s3Service *aws.S3Service) *VideoHandler {
	return &VideoHandler{
		s3Service: s3Service,
	}
}

func (h *VideoHandler) GetVideoURL(c *gin.Context) {
	if h.s3Service == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "S3 integration not configured",
			"message": "Video playback is not available",
		})
		return
	}

	s3Key := c.Param("s3Key")

	if s3Key == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "S3 key is required"})
		return
	}

	// Remove leading slash from wildcard parameter
	if strings.HasPrefix(s3Key, "/") {
		s3Key = s3Key[1:]
	}

	// Decode the S3 key if it was URL encoded
	decodedKey, err := url.QueryUnescape(s3Key)
	if err != nil {
		log.Printf("Failed to decode S3 key %s: %v", s3Key, err)
		decodedKey = s3Key // Use original if decoding fails
	}

	// Check if object exists
	exists, err := h.s3Service.ObjectExists(decodedKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check object existence"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Video not found"})
		return
	}

	// Generate presigned URL with 2-hour expiration
	videoURL, err := h.s3Service.GeneratePresignedURL(decodedKey, time.Hour*2)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate video URL"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"videoUrl":  videoURL,
		"expiresIn": "2 hours",
		"s3Key":     decodedKey,
	})
}

func (h *VideoHandler) GetScreenshotURL(c *gin.Context) {
	if h.s3Service == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "S3 integration not configured",
			"message": "Screenshot viewing is not available",
		})
		return
	}

	s3Key := c.Param("s3Key")

	if s3Key == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "S3 key is required"})
		return
	}

	// Remove leading slash from wildcard parameter
	if strings.HasPrefix(s3Key, "/") {
		s3Key = s3Key[1:]
	}

	// Decode the S3 key if it was URL encoded
	decodedKey, err := url.QueryUnescape(s3Key)
	if err != nil {
		log.Printf("Failed to decode S3 key %s: %v", s3Key, err)
		decodedKey = s3Key // Use original if decoding fails
	}

	// Check if object exists
	exists, err := h.s3Service.ObjectExists(decodedKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check object existence"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Screenshot not found"})
		return
	}

	// Generate presigned URL with 2-hour expiration
	screenshotURL, err := h.s3Service.GeneratePresignedURL(decodedKey, time.Hour*2)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate screenshot URL"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"screenshotUrl": screenshotURL,
		"expiresIn":     "2 hours",
		"s3Key":         decodedKey,
	})
}

// GetAttachmentURL handles GET /api/v1/attachments/*s3Key - generic endpoint for any file type
func (h *VideoHandler) GetAttachmentURL(c *gin.Context) {
	if h.s3Service == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "S3 integration not configured",
			"message": "Attachment download is not available",
		})
		return
	}

	s3Key := c.Param("s3Key")

	if s3Key == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "S3 key is required"})
		return
	}

	// Remove leading slash from wildcard parameter
	if strings.HasPrefix(s3Key, "/") {
		s3Key = s3Key[1:]
	}

	// Decode the S3 key if it was URL encoded
	decodedKey, err := url.QueryUnescape(s3Key)
	if err != nil {
		log.Printf("Failed to decode S3 key %s: %v", s3Key, err)
		decodedKey = s3Key // Use original if decoding fails
	}

	log.Printf("Getting attachment URL for S3 key: %s", decodedKey)

	// Check if object exists
	exists, err := h.s3Service.ObjectExists(decodedKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check object existence"})
		return
	}

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Attachment not found"})
		return
	}

	// Generate presigned URL with 2-hour expiration
	attachmentURL, err := h.s3Service.GeneratePresignedURL(decodedKey, time.Hour*2)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate attachment URL"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"attachmentUrl": attachmentURL,
		"expiresIn":     "2 hours",
		"s3Key":         decodedKey,
	})
}

func (h *VideoHandler) GetS3Status(c *gin.Context) {
	if h.s3Service == nil {
		c.JSON(http.StatusOK, gin.H{
			"s3Enabled": false,
			"message":   "S3 integration not configured",
		})
		return
	}

	bucket := h.s3Service.GetBucketName()

	c.JSON(http.StatusOK, gin.H{
		"bucket":    bucket,
		"connected": true,
		"timestamp": time.Now().UTC(),
	})
}
