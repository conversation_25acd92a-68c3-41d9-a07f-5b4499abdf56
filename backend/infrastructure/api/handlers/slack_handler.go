package handlers

import (
	"cbi-e2e-analytics/application/usecases"
	"cbi-e2e-analytics/domain/services"
	"cbi-e2e-analytics/domain/valueobjects"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// <PERSON><PERSON>ckHandler handles Slack notification requests
type Slack<PERSON><PERSON>ler struct {
	analyticsUseCase *usecases.AnalyticsUseCase
	slackService     services.SlackNotifier
}

// NewSlackHandler creates a new Slack handler
func NewSlackHandler(analyticsUseCase *usecases.AnalyticsUseCase, slackService services.SlackNotifier) *<PERSON><PERSON>ckHandler {
	return &SlackHandler{
		analyticsUseCase: analyticsUseCase,
		slackService:     slackService,
	}
}

// SlackNotificationRequest represents the incoming request for Slack notifications
type SlackNotificationRequest struct {
	TimeRange string `json:"timeRange" binding:"required"`
	StartDate string `json:"startDate,omitempty"`
	EndDate   string `json:"endDate,omitempty"`
}

// SendNotification handles POST /api/slack/notify
func (h *<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>) SendNotification(c *gin.Context) {
	var req SlackNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format: " + err.Error()})
		return
	}

	// Validate Slack configuration first
	if err := h.slackService.ValidateConfiguration(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Slack configuration validation failed",
			"details": err.Error(),
		})
		return
	}

	// Parse filters based on time range
	filters, err := h.parseTimeRangeFilters(req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid time range: " + err.Error()})
		return
	}

	// Get dashboard metrics for the specified time range
	metrics, err := h.analyticsUseCase.GetDashboardMetrics(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get metrics: " + err.Error()})
		return
	}

	// Get flaky tests count from metrics
	flakyTests := metrics.TestsByStatus["flaky"]

	// Create Slack notification request
	slackReq := services.SlackNotificationRequest{
		TimeRange:   req.TimeRange,
		StartDate:   req.StartDate,
		EndDate:     req.EndDate,
		TotalTests:  metrics.TotalTests,
		PassedTests: metrics.PassedTests,
		FailedTests: metrics.FailedTests,
		FlakyTests:  flakyTests,
		PassRate:    metrics.OverallPassRate,
	}

	// Send Slack notification
	if err := h.slackService.SendTestReport(slackReq); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send Slack notification: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Slack notification sent successfully",
		"metrics": gin.H{
			"totalTests":  metrics.TotalTests,
			"passedTests": metrics.PassedTests,
			"failedTests": metrics.FailedTests,
			"flakyTests":  flakyTests,
			"passRate":    metrics.OverallPassRate,
		},
	})
}

// parseTimeRangeFilters converts time range request to filter criteria
func (h *SlackHandler) parseTimeRangeFilters(req SlackNotificationRequest) (*valueobjects.FilterCriteria, error) {
	filters := &valueobjects.FilterCriteria{}
	now := time.Now()

	switch req.TimeRange {
	case "24h":
		startTime := now.Add(-24 * time.Hour)
		filters.StartDate = &startTime
		filters.EndDate = &now
	case "7d":
		startTime := now.Add(-7 * 24 * time.Hour)
		filters.StartDate = &startTime
		filters.EndDate = &now
	case "30d":
		startTime := now.Add(-30 * 24 * time.Hour)
		filters.StartDate = &startTime
		filters.EndDate = &now
	case "custom":
		if req.StartDate == "" || req.EndDate == "" {
			return nil, fmt.Errorf("start date and end date are required for custom range")
		}
		// Validate and parse date format
		startDate, err := time.Parse("2006-01-02", req.StartDate)
		if err != nil {
			return nil, fmt.Errorf("invalid start date format, expected YYYY-MM-DD")
		}
		endDate, err := time.Parse("2006-01-02", req.EndDate)
		if err != nil {
			return nil, fmt.Errorf("invalid end date format, expected YYYY-MM-DD")
		}
		filters.StartDate = &startDate
		filters.EndDate = &endDate
	case "all":
		// No date filters for all time
	default:
		return nil, fmt.Errorf("invalid time range: %s", req.TimeRange)
	}

	return filters, nil
}

// TestSlackConnection handles GET /api/slack/test
func (h *SlackHandler) TestSlackConnection(c *gin.Context) {
	// First validate the configuration
	if err := h.slackService.ValidateConfiguration(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Slack configuration validation failed",
			"details": err.Error(),
			"suggestions": []string{
				"1. Verify your Slack token is correct and has the required permissions",
				"2. Ensure the channel name is correct (without #)",
				"3. Make sure the bot is added to the channel",
				"4. Check that the token has 'chat:write' and 'channels:read' scopes",
			},
		})
		return
	}

	// Simple test message
	testReq := services.SlackNotificationRequest{
		TimeRange:   "test",
		TotalTests:  100,
		PassedTests: 85,
		FailedTests: 10,
		FlakyTests:  5,
		PassRate:    85.0,
	}

	if err := h.slackService.SendTestReport(testReq); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to send test message to Slack",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Test message sent to Slack successfully! Check your #ztb-e2e-report-alerts channel.",
	})
}
