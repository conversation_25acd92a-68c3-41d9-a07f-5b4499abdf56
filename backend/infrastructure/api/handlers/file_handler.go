package handlers

import (
	"cbi-e2e-analytics/application/usecases"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// FileHandler handles file upload and management operations
type FileHandler struct {
	analyticsUseCase *usecases.AnalyticsUseCase
}

// NewFileHandler creates a new file handler
func NewFileHandler(analyticsUseCase *usecases.AnalyticsUseCase) *FileHandler {
	return &FileHandler{
		analyticsUseCase: analyticsUseCase,
	}
}

// UploadTestResult handles POST /api/files/upload
func (h *FileHandler) UploadTestResult(c *gin.Context) {
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
		return
	}
	defer file.Close()

	// Validate file extension
	if !strings.HasSuffix(header.Filename, ".json") {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Only JSON files are allowed"})
		return
	}

	// Read file content
	buf := make([]byte, header.Size)
	_, err = file.Read(buf)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read file"})
		return
	}

	// Parse JSON to validate it's a valid Playwright result
	var playwrightResult map[string]interface{}
	err = json.Unmarshal(buf, &playwrightResult)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON format"})
		return
	}

	// Basic validation for Playwright structure
	if _, hasConfig := playwrightResult["config"]; !hasConfig {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid Playwright test result format - missing config"})
		return
	}

	if _, hasSuites := playwrightResult["suites"]; !hasSuites {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid Playwright test result format - missing suites"})
		return
	}

	// Create a unique filename if one with the same name already exists
	fileName := h.generateUniqueFileName(header.Filename)

	// Save the original file content directly
	err = h.saveOriginalFile(buf, fileName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "File uploaded successfully",
		"fileName": fileName,
		"fileId":   uuid.New().String(),
	})
}

// GetFiles handles GET /api/files
func (h *FileHandler) GetFiles(c *gin.Context) {
	files, err := h.analyticsUseCase.GetAvailableFiles()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Enhance file information with actual file sizes
	fileInfos := make([]map[string]interface{}, len(files))
	for i, fileName := range files {
		fileSize := h.getFileSize(fileName)
		fileInfos[i] = map[string]interface{}{
			"name": fileName,
			"size": fileSize,
			"type": "application/json",
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"files": fileInfos,
		"count": len(files),
	})
}

// DeleteFile handles DELETE /api/files/:filename
func (h *FileHandler) DeleteFile(c *gin.Context) {
	fileName := c.Param("filename")
	
	if fileName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Filename is required"})
		return
	}

	// Validate filename to prevent directory traversal
	if strings.Contains(fileName, "..") || strings.Contains(fileName, "/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid filename"})
		return
	}

	err := h.analyticsUseCase.DeleteTestResultFile(fileName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "File deleted successfully",
		"fileName": fileName,
	})
}

// UploadJSONText handles POST /api/files/upload-json-text
func (h *FileHandler) UploadJSONText(c *gin.Context) {
	var request struct {
		JSONContent string `json:"jsonContent" binding:"required"`
		FileName    string `json:"fileName"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	// Validate JSON content
	var playwrightResult map[string]interface{}
	err := json.Unmarshal([]byte(request.JSONContent), &playwrightResult)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid JSON format",
			"valid": false,
		})
		return
	}

	// Basic validation for Playwright structure
	if _, hasConfig := playwrightResult["config"]; !hasConfig {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid Playwright test result format - missing config",
			"valid": false,
		})
		return
	}

	if _, hasSuites := playwrightResult["suites"]; !hasSuites {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid Playwright test result format - missing suites",
			"valid": false,
		})
		return
	}

	// Generate filename if not provided
	fileName := request.FileName
	if fileName == "" {
		fileName = fmt.Sprintf("pasted-json-%d.json", time.Now().Unix())
	} else if !strings.HasSuffix(fileName, ".json") {
		fileName = fileName + ".json"
	}

	// Create a unique filename if one with the same name already exists
	fileName = h.generateUniqueFileName(fileName)

	// Save the JSON content
	err = h.saveOriginalFile([]byte(request.JSONContent), fileName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "JSON content uploaded successfully",
		"fileName": fileName,
		"fileId":   uuid.New().String(),
		"valid":    true,
	})
}

// UploadMultipleFiles handles POST /api/files/upload-multiple
func (h *FileHandler) UploadMultipleFiles(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse multipart form"})
		return
	}

	files := form.File["files"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No files uploaded"})
		return
	}

	var uploadedFiles []string
	var errors []string

	for _, fileHeader := range files {
		// Validate file extension
		if !strings.HasSuffix(fileHeader.Filename, ".json") {
			errors = append(errors, fmt.Sprintf("%s: Only JSON files are allowed", fileHeader.Filename))
			continue
		}

		file, err := fileHeader.Open()
		if err != nil {
			errors = append(errors, fmt.Sprintf("%s: Failed to open file", fileHeader.Filename))
			continue
		}

		// Read file content
		buf := make([]byte, fileHeader.Size)
		_, err = file.Read(buf)
		file.Close()
		if err != nil {
			errors = append(errors, fmt.Sprintf("%s: Failed to read file", fileHeader.Filename))
			continue
		}

		// Parse JSON to validate it's a valid Playwright result
		var playwrightResult map[string]interface{}
		err = json.Unmarshal(buf, &playwrightResult)
		if err != nil {
			errors = append(errors, fmt.Sprintf("%s: Invalid JSON format", fileHeader.Filename))
			continue
		}

		// Basic validation for Playwright structure
		if _, hasConfig := playwrightResult["config"]; !hasConfig {
			errors = append(errors, fmt.Sprintf("%s: Invalid Playwright format - missing config", fileHeader.Filename))
			continue
		}

		if _, hasSuites := playwrightResult["suites"]; !hasSuites {
			errors = append(errors, fmt.Sprintf("%s: Invalid Playwright format - missing suites", fileHeader.Filename))
			continue
		}

		// Create a unique filename
		fileName := h.generateUniqueFileName(fileHeader.Filename)

		// Save the original file content directly
		err = h.saveOriginalFile(buf, fileName)
		if err != nil {
			errors = append(errors, fmt.Sprintf("%s: Failed to save file", fileHeader.Filename))
			continue
		}

		uploadedFiles = append(uploadedFiles, fileName)
	}

	response := gin.H{
		"uploadedFiles": uploadedFiles,
		"uploadedCount": len(uploadedFiles),
		"totalFiles":    len(files),
	}

	if len(errors) > 0 {
		response["errors"] = errors
		response["errorCount"] = len(errors)
	}

	statusCode := http.StatusOK
	if len(uploadedFiles) == 0 {
		statusCode = http.StatusBadRequest
	} else if len(errors) > 0 {
		statusCode = http.StatusPartialContent
	}

	c.JSON(statusCode, response)
}

// generateUniqueFileName generates a unique filename to avoid conflicts
func (h *FileHandler) generateUniqueFileName(originalName string) string {
	ext := filepath.Ext(originalName)
	nameWithoutExt := strings.TrimSuffix(originalName, ext)

	// Add timestamp to make it unique
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s_%s%s", nameWithoutExt, timestamp, ext)
}

// getFileSize calculates and formats the file size
func (h *FileHandler) getFileSize(fileName string) string {
	// Get the data path from the analytics use case
	files, err := h.analyticsUseCase.GetAvailableFiles()
	if err != nil {
		return "Unknown"
	}

	// Find the file in the list to ensure it exists
	found := false
	for _, file := range files {
		if file == fileName {
			found = true
			break
		}
	}

	if !found {
		return "Unknown"
	}

	// Construct the file path (assuming data is in json-test-results directory)
	// We need to get the data path from somewhere - let's use a reasonable default
	dataPath := "./json-test-results" // This should ideally come from config
	filePath := filepath.Join(dataPath, fileName)

	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return "Unknown"
	}

	return h.formatFileSize(fileInfo.Size())
}

// formatFileSize formats file size in human-readable format
func (h *FileHandler) formatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}

	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}

	units := []string{"KB", "MB", "GB", "TB"}
	return fmt.Sprintf("%.1f %s", float64(size)/float64(div), units[exp])
}

// saveOriginalFile saves the original file content without any transformation
func (h *FileHandler) saveOriginalFile(content []byte, fileName string) error {
	dataPath := "./json-test-results" // This should ideally come from config
	filePath := filepath.Join(dataPath, fileName)

	err := os.WriteFile(filePath, content, 0644)
	if err != nil {
		return fmt.Errorf("failed to write file %s: %w", filePath, err)
	}

	return nil
}

// GetFileStats handles GET /api/files/stats
func (h *FileHandler) GetFileStats(c *gin.Context) {
	files, err := h.analyticsUseCase.GetAvailableFiles()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	stats := gin.H{
		"totalFiles": len(files),
		"fileTypes": map[string]int{
			"json": len(files),
		},
	}

	c.JSON(http.StatusOK, stats)
}
