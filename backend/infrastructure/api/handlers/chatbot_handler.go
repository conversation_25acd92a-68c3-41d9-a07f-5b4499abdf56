package handlers

import (
	"context"
	"net/http"
	"time"

	"cbi-e2e-analytics/application/usecases"
	"cbi-e2e-analytics/domain/services"
	"cbi-e2e-analytics/domain/valueobjects"

	"github.com/gin-gonic/gin"
)

// <PERSON><PERSON>bot<PERSON>andler handles chatbot-related requests
type Chatbot<PERSON><PERSON>ler struct {
	chatbotService   *services.ChatbotService
	analyticsUseCase *usecases.AnalyticsUseCase
}

// NewChatbotHandler creates a new chatbot handler
func NewChatbotHandler(chatbotService *services.ChatbotService, analyticsUseCase *usecases.AnalyticsUseCase) *Chatbot<PERSON>and<PERSON> {
	return &ChatbotHandler{
		chatbotService:   chatbotService,
		analyticsUseCase: analyticsUseCase,
	}
}

// ChatRequest represents the incoming chat request
type ChatRequest struct {
	Message string                 `json:"message" binding:"required"`
	Context map[string]interface{} `json:"context,omitempty"`
	Filters map[string]interface{} `json:"filters,omitempty"`
}

// ChatResponse represents the chat response
type ChatResponse struct {
	Message     string                 `json:"message"`
	Data        interface{}            `json:"data,omitempty"`
	Suggestions []string               `json:"suggestions,omitempty"`
	ChartData   interface{}            `json:"chartData,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
	Success     bool                   `json:"success"`
}

// ProcessChat handles chat messages and returns AI-powered responses
func (h *ChatbotHandler) ProcessChat(c *gin.Context) {
	var request ChatRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Get test results for analysis
	testResults, err := h.analyticsUseCase.GetAllTestResults()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to load test data",
			"details": err.Error(),
		})
		return
	}

	// Convert filters if provided
	var filters *valueobjects.FilterCriteria
	if request.Filters != nil {
		filters = h.convertFilters(request.Filters)
	}

	// Create service request
	serviceRequest := services.ChatRequest{
		Message: request.Message,
		Context: request.Context,
		Filters: filters,
	}

	// Process the query
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := h.chatbotService.ProcessQuery(ctx, serviceRequest, testResults)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process query",
			"details": err.Error(),
		})
		return
	}

	// Return successful response
	c.JSON(http.StatusOK, ChatResponse{
		Message:     response.Message,
		Data:        response.Data,
		Suggestions: response.Suggestions,
		ChartData:   response.ChartData,
		Timestamp:   response.Timestamp,
		Success:     true,
	})
}

// GetChatHistory returns recent chat interactions (placeholder for future implementation)
func (h *ChatbotHandler) GetChatHistory(c *gin.Context) {
	// For now, return empty history
	c.JSON(http.StatusOK, gin.H{
		"history": []interface{}{},
		"success": true,
	})
}

// GetSuggestions returns suggested questions based on current data
func (h *ChatbotHandler) GetSuggestions(c *gin.Context) {
	suggestions := []string{
		"Show me an overview of my test results",
		"Which tests are failing the most?",
		"What are the slowest tests in my suite?",
		"How is my test performance trending?",
		"Compare performance across projects",
		"Show me flaky tests",
		"What are the most common error patterns?",
		"Which tests have the highest retry rates?",
		"Show me tests that timeout frequently",
		"What's the overall health of my test suite?",
	}

	c.JSON(http.StatusOK, gin.H{
		"suggestions": suggestions,
		"success":     true,
	})
}

// GetChatStatus returns the status of the chatbot service
func (h *ChatbotHandler) GetChatStatus(c *gin.Context) {
	// Get basic stats about available data
	testResults, err := h.analyticsUseCase.GetAllTestResults()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"available":   false,
			"message":     "No test data available",
			"dataPoints":  0,
			"lastUpdated": nil,
		})
		return
	}

	// Calculate basic stats
	totalTests := 0
	var lastUpdated time.Time
	for _, result := range testResults {
		for _, suite := range result.Suites {
			for _, spec := range suite.Specs {
				totalTests += len(spec.Tests)
			}
		}
		if result.Stats.StartTime.After(lastUpdated) {
			lastUpdated = result.Stats.StartTime
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"available":   true,
		"message":     "Chatbot ready to analyze your test data",
		"dataPoints":  totalTests,
		"testFiles":   len(testResults),
		"lastUpdated": lastUpdated,
		"success":     true,
	})
}

// Helper method to convert filters from map to FilterCriteria
func (h *ChatbotHandler) convertFilters(filterMap map[string]interface{}) *valueobjects.FilterCriteria {
	filters := &valueobjects.FilterCriteria{}

	if startDate, ok := filterMap["startDate"].(string); ok && startDate != "" {
		if parsed, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &parsed
		}
	}

	if endDate, ok := filterMap["endDate"].(string); ok && endDate != "" {
		if parsed, err := time.Parse("2006-01-02", endDate); err == nil {
			filters.EndDate = &parsed
		}
	}

	if status, ok := filterMap["status"].([]interface{}); ok {
		var statusList []string
		for _, s := range status {
			if str, ok := s.(string); ok {
				statusList = append(statusList, str)
			}
		}
		filters.Status = statusList
	}

	if projects, ok := filterMap["projectName"].([]interface{}); ok {
		var projectList []string
		for _, p := range projects {
			if str, ok := p.(string); ok {
				projectList = append(projectList, str)
			}
		}
		filters.ProjectName = projectList
	}

	if tags, ok := filterMap["tags"].([]interface{}); ok {
		var tagList []string
		for _, t := range tags {
			if str, ok := t.(string); ok {
				tagList = append(tagList, str)
			}
		}
		filters.Tags = tagList
	}

	return filters
}
