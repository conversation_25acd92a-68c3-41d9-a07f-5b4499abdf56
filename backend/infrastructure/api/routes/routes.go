package routes

import (
	"cbi-e2e-analytics/infrastructure/api/handlers"
	"fmt"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all API routes
func SetupRoutes(
	analyticsHandler *handlers.AnalyticsHandler,
	fileHandler *handlers.FileHandler,
	slackHandler *handlers.SlackHandler,
	videoHandler *handlers.VideoHandler,
	s3Handler *handlers.S3Handler,
	chatbotHandler *handlers.ChatbotHandler,
) *gin.Engine {
	// Create Gin router
	router := gin.Default()

	// Configure CORS with comprehensive settings
	config := cors.Config{
		AllowOrigins:     []string{"http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:5173", "http://127.0.0.1:3000", "http://127.0.0.1:3001", "http://127.0.0.1:3002", "http://127.0.0.1:5173"},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With", "Accept", "Accept-Encoding", "Accept-Language", "Connection", "Host"},
		ExposeHeaders:    []string{"Content-Length", "Content-Type"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}
	router.Use(cors.New(config))

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Health check
		v1.GET("/health", analyticsHandler.HealthCheck)

		// Analytics routes
		analytics := v1.Group("/analytics")
		{
			analytics.GET("/dashboard", analyticsHandler.GetDashboardMetrics)
			analytics.GET("/tests", analyticsHandler.GetTestAnalytics)
			analytics.GET("/test-runs", analyticsHandler.GetTestRuns)
			analytics.GET("/error-categories", analyticsHandler.GetErrorCategoriesWithTests)
			analytics.GET("/charts/:type", analyticsHandler.GetChartData)
			analytics.GET("/wordcloud", analyticsHandler.GetWordCloudData)
			analytics.GET("/sunburst", analyticsHandler.GetSunburstData)
			analytics.GET("/bubble", analyticsHandler.GetBubbleChartData)
			analytics.GET("/heatmap", analyticsHandler.GetHeatmapData)
			analytics.GET("/filter-options", analyticsHandler.GetFilterOptions)
			analytics.GET("/available-dates", analyticsHandler.GetAvailableDates)
		}

		// File management routes
		files := v1.Group("/files")
		{
			files.GET("", fileHandler.GetFiles)
			files.POST("/upload", fileHandler.UploadTestResult)
			files.POST("/upload-multiple", fileHandler.UploadMultipleFiles)
			files.POST("/upload-json-text", fileHandler.UploadJSONText)
			files.DELETE("/:filename", fileHandler.DeleteFile)
			files.GET("/stats", fileHandler.GetFileStats)
		}

		// Slack notification routes
		slack := v1.Group("/slack")
		{
			slack.POST("/notify", slackHandler.SendNotification)
			slack.GET("/test", slackHandler.TestSlackConnection)
		}

		// Video routes
		videos := v1.Group("/videos")
		{
			videos.GET("/*s3Key", videoHandler.GetVideoURL)
		}

		// Video status route (separate path to avoid wildcard conflict)
		v1.GET("/video-status", videoHandler.GetS3Status)

		// Screenshot routes
		screenshots := v1.Group("/screenshots")
		{
			screenshots.GET("/*s3Key", videoHandler.GetScreenshotURL)
		}

		// Generic attachment routes
		attachments := v1.Group("/attachments")
		{
			attachments.GET("/*s3Key", videoHandler.GetAttachmentURL)
		}

		// S3 management routes
		s3Routes := v1.Group("/s3")
		{
			s3Routes.GET("/status", s3Handler.GetS3Status)
			s3Routes.POST("/sync", s3Handler.TriggerSync)
			s3Routes.POST("/clear-cache", s3Handler.ClearCache)
			s3Routes.GET("/max-files", s3Handler.GetMaxFiles)
			s3Routes.PUT("/max-files/:maxFiles", s3Handler.SetMaxFiles)
		}

		// Chatbot routes
		chatbot := v1.Group("/chatbot")
		{
			chatbot.POST("/chat", chatbotHandler.ProcessChat)
			chatbot.GET("/history", chatbotHandler.GetChatHistory)
			chatbot.GET("/suggestions", chatbotHandler.GetSuggestions)
			chatbot.GET("/status", chatbotHandler.GetChatStatus)
		}
	}

	// Legacy API routes (for backward compatibility)
	api := router.Group("/api")
	{
		// Health check
		api.GET("/health", analyticsHandler.HealthCheck)

		// Analytics routes
		analytics := api.Group("/analytics")
		{
			analytics.GET("/dashboard", analyticsHandler.GetDashboardMetrics)
			analytics.GET("/tests", analyticsHandler.GetTestAnalytics)
			analytics.GET("/test-runs", analyticsHandler.GetTestRuns)
			analytics.GET("/error-categories", analyticsHandler.GetErrorCategoriesWithTests)
			analytics.GET("/charts/:type", analyticsHandler.GetChartData)
			analytics.GET("/wordcloud", analyticsHandler.GetWordCloudData)
			analytics.GET("/sunburst", analyticsHandler.GetSunburstData)
			analytics.GET("/bubble", analyticsHandler.GetBubbleChartData)
			analytics.GET("/heatmap", analyticsHandler.GetHeatmapData)
			analytics.GET("/filter-options", analyticsHandler.GetFilterOptions)
			analytics.GET("/available-dates", analyticsHandler.GetAvailableDates)
		}

		// File management routes
		files := api.Group("/files")
		{
			files.GET("", fileHandler.GetFiles)
			files.POST("/upload", fileHandler.UploadTestResult)
			files.POST("/upload-multiple", fileHandler.UploadMultipleFiles)
			files.POST("/upload-json-text", fileHandler.UploadJSONText)
			files.DELETE("/:filename", fileHandler.DeleteFile)
			files.GET("/stats", fileHandler.GetFileStats)
		}

		// Slack notification routes
		slack := api.Group("/slack")
		{
			slack.POST("/notify", slackHandler.SendNotification)
			slack.GET("/test", slackHandler.TestSlackConnection)
		}
	}

	return router
}

// SetupMiddleware configures additional middleware
func SetupMiddleware(router *gin.Engine) {
	// Request logging middleware
	router.Use(gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	}))

	// Recovery middleware
	router.Use(gin.Recovery())

	// Security headers middleware
	router.Use(func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Next()
	})
}

// Additional helper functions for route configuration
func (r *Routes) setupStaticRoutes(router *gin.Engine) {
	// Serve static files if needed
	router.Static("/static", "./static")
	router.StaticFile("/favicon.ico", "./static/favicon.ico")
}

type Routes struct {
	analyticsHandler *handlers.AnalyticsHandler
	fileHandler      *handlers.FileHandler
	slackHandler     *handlers.SlackHandler
	videoHandler     *handlers.VideoHandler
	s3Handler        *handlers.S3Handler
	chatbotHandler   *handlers.ChatbotHandler
}

func NewRoutes(analyticsHandler *handlers.AnalyticsHandler, fileHandler *handlers.FileHandler, slackHandler *handlers.SlackHandler, videoHandler *handlers.VideoHandler, s3Handler *handlers.S3Handler, chatbotHandler *handlers.ChatbotHandler) *Routes {
	return &Routes{
		analyticsHandler: analyticsHandler,
		fileHandler:      fileHandler,
		slackHandler:     slackHandler,
		videoHandler:     videoHandler,
		s3Handler:        s3Handler,
		chatbotHandler:   chatbotHandler,
	}
}

func (r *Routes) Setup() *gin.Engine {
	router := SetupRoutes(r.analyticsHandler, r.fileHandler, r.slackHandler, r.videoHandler, r.s3Handler, r.chatbotHandler)
	SetupMiddleware(router)
	r.setupStaticRoutes(router)
	return router
}
