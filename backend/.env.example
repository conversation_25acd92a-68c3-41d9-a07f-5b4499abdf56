# Server Configuration
PORT=8080
HOST=localhost
GIN_MODE=debug

# Database Configuration
DATABASE_URL=./data/test_results.db

# Feature Flags
ENABLE_S3_INTEGRATION=false

# AWS S3 Configuration (only needed if S3 integration is enabled)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=
AWS_S3_VIDEO_PREFIX=test-videos/
AWS_S3_DATA_PREFIX=test-results/
S3_SYNC_INTERVAL_MINUTES=5
S3_MAX_FILES=10

# Slack Configuration
SLACK_TOKEN=
SLACK_CHANNEL=
SLACK_WEBHOOK_URL=

# Data Path (optional)
DATA_PATH=./json-test-results
