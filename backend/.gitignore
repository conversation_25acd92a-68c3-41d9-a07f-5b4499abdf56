# Go build artifacts
bin/
*.exe
*.exe~
*.dll
*.so
*.dylib

# Go test binary, built with `go test -c`
*.test

# Go coverage files
*.out
*.prof

# Go workspace file
go.work

# Log files
*.log
logs/
server.log
server_debug.log
server_new.log
server_output.log
backend.log

# JSON test results (except .gitkeep)
json-test-results/*
!json-test-results/.gitkeep

# Temporary response files from testing/debugging
*_response*.json
dashboard_*.json
debug.json

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp
