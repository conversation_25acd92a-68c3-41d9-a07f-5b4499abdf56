# 🤖 Chatbot Testing Guide

## Test the Floating Chatbot

### **1. Start the Application**
```bash
# Backend
cd backend
go run main.go

# Frontend (in another terminal)
cd frontend
npm run dev
```

### **2. Test Chatbot Functionality**

#### **Basic Questions to Try:**
1. "Show me an overview of my test results"
2. "Which tests are failing?"
3. "What are the slowest tests?"
4. "How is my test performance trending?"
5. "Compare performance across projects"
6. "Show me flaky tests"
7. "What are the most common error patterns?"

#### **Expected Responses:**
- The chatbot should provide data-driven insights
- Responses should include specific numbers and percentages
- Suggestions should appear for follow-up questions
- Loading states should work properly

### **3. API Endpoints to Test**

#### **Chatbot Status**
```bash
curl http://localhost:8080/api/v1/chatbot/status
```

#### **Get Suggestions**
```bash
curl http://localhost:8080/api/v1/chatbot/suggestions
```

#### **Send Chat Message**
```bash
curl -X POST http://localhost:8080/api/v1/chatbot/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Show me an overview of my tests",
    "context": {},
    "filters": {}
  }'
```

### **4. Frontend Features to Test**

#### **Floating Button**
- ✅ Button appears in bottom-right corner
- ✅ Hover tooltip shows "Ask questions about your test data"
- ✅ Gradient background and smooth animations

#### **Chat Drawer**
- ✅ Opens from right side
- ✅ Shows chatbot avatar and status
- ✅ Displays data points and file count

#### **Messaging**
- ✅ Welcome message appears on first open
- ✅ User messages appear on right (blue)
- ✅ Bot messages appear on left (gray)
- ✅ Timestamps are shown
- ✅ Loading indicator during processing

#### **Suggestions**
- ✅ Suggestion tags appear below bot messages
- ✅ Clicking suggestions sends them as messages
- ✅ Suggestions are contextual and helpful

#### **Input Area**
- ✅ Multi-line text area
- ✅ Send button enabled/disabled based on input
- ✅ Enter key sends message (Shift+Enter for new line)
- ✅ Input disabled during loading

### **5. MCP Integration Testing**

The chatbot includes MCP (Model Context Protocol) integration:

#### **Mock Response Testing**
- Currently returns mock responses for testing
- Includes actual test data in context
- Provides structured insights

#### **Real MCP Integration** (Future)
To enable real MCP integration:
1. Configure MCP service endpoint
2. Update `sendMCPRequest` method in `mcp_service.go`
3. Add authentication if required
4. Test with real AI model

### **6. Data Context Testing**

The chatbot has access to:
- ✅ Total test counts and statistics
- ✅ Pass/fail rates and trends
- ✅ Project-specific metrics
- ✅ Performance data (duration, timeouts)
- ✅ Error patterns and failure analysis

### **7. Error Handling**

Test error scenarios:
- ✅ No test data available
- ✅ Network connectivity issues
- ✅ Invalid queries
- ✅ Service timeouts

### **8. Performance Testing**

- ✅ Response times under 5 seconds
- ✅ Smooth animations and transitions
- ✅ No memory leaks with multiple conversations
- ✅ Proper cleanup when drawer is closed

## 🎯 **Success Criteria**

The chatbot implementation is successful if:

1. **Functional**: All API endpoints work correctly
2. **Interactive**: UI is responsive and user-friendly
3. **Intelligent**: Provides meaningful insights from test data
4. **Contextual**: Understands test automation terminology
5. **Helpful**: Offers relevant suggestions and follow-ups
6. **Reliable**: Handles errors gracefully
7. **Performant**: Fast response times and smooth UX

## 🚀 **Next Steps**

1. **Real MCP Integration**: Connect to actual AI service
2. **Chat History**: Implement conversation persistence
3. **Advanced Analytics**: Add more sophisticated data analysis
4. **Custom Filters**: Allow users to apply filters through chat
5. **Export Features**: Enable exporting insights and reports
6. **Voice Input**: Add speech-to-text capabilities
7. **Scheduled Reports**: Automated insights delivery

The floating chatbot is now ready to help users analyze their test data with AI-powered insights! 🎉
