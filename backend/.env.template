# S3 Integration Configuration
ENABLE_S3_INTEGRATION=true

# AWS Configuration - REPLACE WITH YOUR ACTUAL VALUES
AWS_REGION=eu-central-1
# Option 1: Use explicit credentials (make sure they're valid and not expired)
# AWS_ACCESS_KEY_ID=your_access_key_here
# AWS_SECRET_ACCESS_KEY=your_secret_key_here
# AWS_SESSION_TOKEN=your_session_token_here
# Option 2: Leave empty to use AWS profile/IAM role (comment out the above lines)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=appsulate-j<PERSON><PERSON>-reports


# S3 Path Prefixes - Based on your bucket structure
# Your structure: dev/ourl-lemon/2025-07-13T18:30:00Z-pw-originalurl-nightly-1752431400-e2e-test-1686646514/playwright-report/data/
# The system will scan for JSON files in playwright-report/data/ subdirectories
AWS_S3_DATA_PREFIX=workflowtest/dev/ourl-lemon/
AWS_S3_VIDEO_PREFIX=workflowtest/dev/ourl-lemon/
AWS_S3_SCREENSHOT_PREFIX=workflowtest/dev/ourl-lemon/

# Sync Configuration
S3_SYNC_INTERVAL_MINUTES=5
S3_SYNC_DAYS=2

# Server Configuration
PORT=8080
HOST=localhost

# Database Configuration
DATABASE_URL=./data/test_results.db

# Slack Configuration (Not configured for now)
SLACK_TOKEN=
SLACK_CHANNEL=
SLACK_WEBHOOK_URL=
