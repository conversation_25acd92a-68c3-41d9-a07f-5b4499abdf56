package main

import (
	"cbi-e2e-analytics/application/usecases"
	"cbi-e2e-analytics/config"
	"cbi-e2e-analytics/domain/services"
	"cbi-e2e-analytics/infrastructure/api/handlers"
	"cbi-e2e-analytics/infrastructure/api/routes"
	"cbi-e2e-analytics/infrastructure/aws"
	"cbi-e2e-analytics/infrastructure/mcp"
	"cbi-e2e-analytics/infrastructure/repositories"
	"cbi-e2e-analytics/infrastructure/scheduler"
	"log"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load configuration:", err)
	}

	log.Printf("S3 Integration enabled: %v", cfg.Features.S3Integration)
	log.Printf("S3 Bucket: %s", cfg.AWS.S3Bucket)
	log.Printf("S3 Max Files: %d", cfg.AWS.S3MaxFiles)

	// Set Gin mode
	if os.Getenv("GIN_MODE") == "" {
		gin.SetMode(gin.DebugMode)
	}

	// Initialize data path
	dataPath := getDataPath()
	ensureDataPathExists(dataPath)

	// Initialize dependencies
	testResultRepo := repositories.NewTestResultRepository(dataPath)
	analyticsService := services.NewAnalyticsService()

	// Initialize MCP service
	mcpService := mcp.NewMCPService(cfg)
	chatbotService := services.NewChatbotService(analyticsService, mcpService)

	analyticsUseCase := usecases.NewAnalyticsUseCase(testResultRepo, analyticsService)

	// Initialize Slack service (supports both webhook and token)
	slackWebhookURL := os.Getenv("SLACK_WEBHOOK_URL")
	var slackService services.SlackNotifier

	if slackWebhookURL != "" {
		// Use webhook approach (easier, no permissions needed)

		slackService = services.NewSlackWebhookService(slackWebhookURL)
	} else {
		// Fallback to token approach
		slackToken := os.Getenv("SLACK_TOKEN")
		if slackToken == "" {
			slackToken = "*****************************************************************************" // Default token

		}
		slackChannel := os.Getenv("SLACK_CHANNEL")
		if slackChannel == "" {
			slackChannel = "C091V2CF5DF" // Default channel ID for ztb-e2e-report-alerts

		}



		slackService = services.NewSlackService(slackToken, slackChannel)
	}

	// Initialize S3 services if configured
	var s3Service *aws.S3Service
	var s3DataSource *aws.S3DataSource
	var s3Scheduler *scheduler.S3Scheduler
	var videoHandler *handlers.VideoHandler
	var s3Handler *handlers.S3Handler

	if cfg.Features.S3Integration && cfg.AWS.S3Bucket != "" {
		log.Println("S3 integration enabled, initializing...")

		s3Config := aws.S3Config{
			Region:          cfg.AWS.Region,
			AccessKeyID:     cfg.AWS.AccessKeyID,
			SecretAccessKey: cfg.AWS.SecretAccessKey,
			SessionToken:    cfg.AWS.SessionToken,
			Bucket:          cfg.AWS.S3Bucket,
		}

		s3Service, err = aws.NewS3Service(s3Config)
		if err != nil {
			log.Printf("Warning: Failed to initialize S3 service: %v", err)
			log.Println("Continuing without S3 integration...")
		} else {
			log.Printf("S3 service initialized for bucket: %s", cfg.AWS.S3Bucket)

			// Initialize S3 data source
			s3DataSource = aws.NewS3DataSource(s3Service, analyticsUseCase, cfg.AWS.S3DataPrefix, cfg.AWS.S3VideoPrefix, cfg.AWS.S3MaxFiles, cfg.AWS.S3SyncDays)

			// Initialize and start S3 scheduler
			s3Scheduler = scheduler.NewS3Scheduler(s3DataSource, cfg.AWS.S3SyncInterval)
			s3Scheduler.Start()

			log.Printf("S3 scheduler started with %d minute interval", cfg.AWS.S3SyncInterval)
		}
	} else {
		log.Println("S3 integration disabled or not configured")
	}

	// Initialize handlers
	analyticsHandler := handlers.NewAnalyticsHandler(analyticsUseCase)
	fileHandler := handlers.NewFileHandler(analyticsUseCase)
	slackHandler := handlers.NewSlackHandler(analyticsUseCase, slackService)
	videoHandler = handlers.NewVideoHandler(s3Service)
	s3Handler = handlers.NewS3Handler(s3Service, s3DataSource, s3Scheduler)
	chatbotHandler := handlers.NewChatbotHandler(chatbotService, analyticsUseCase)

	// Setup routes
	routeManager := routes.NewRoutes(analyticsHandler, fileHandler, slackHandler, videoHandler, s3Handler, chatbotHandler)
	router := routeManager.Setup()

	// Get port from environment or use default
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}



	// Start server
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// getDataPath returns the path where test result JSON files are stored
func getDataPath() string {
	// Check if custom data path is set via environment variable
	if customPath := os.Getenv("DATA_PATH"); customPath != "" {
		return customPath
	}

	// Default to json-test-results directory
	currentDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("Failed to get current directory: %v", err)
	}

	return filepath.Join(currentDir, "json-test-results")
}

// ensureDataPathExists creates the data directory if it doesn't exist
func ensureDataPathExists(dataPath string) {
	if _, err := os.Stat(dataPath); os.IsNotExist(err) {

		err := os.MkdirAll(dataPath, 0755)
		if err != nil {
			log.Fatalf("Failed to create data directory: %v", err)
		}
	}
}

// Additional configuration functions
func setupLogging() {
	// Configure logging format
	log.SetFlags(log.LstdFlags | log.Lshortfile)
}

func printBanner() {
	// Banner removed for production

}

func init() {
	setupLogging()
	printBanner()
}
