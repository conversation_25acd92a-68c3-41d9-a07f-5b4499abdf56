package usecases

import (
	"cbi-e2e-analytics/domain/entities"
	"cbi-e2e-analytics/domain/services"
	"cbi-e2e-analytics/domain/valueobjects"
	"cbi-e2e-analytics/infrastructure/repositories"
	"regexp"
	"strings"
)

// AnalyticsUseCase handles analytics-related business logic
type AnalyticsUseCase struct {
	testResultRepo   *repositories.TestResultRepository
	analyticsService *services.AnalyticsService
}

// NewAnalyticsUseCase creates a new analytics use case
func NewAnalyticsUseCase(testResultRepo *repositories.TestResultRepository, analyticsService *services.AnalyticsService) *AnalyticsUseCase {
	return &AnalyticsUseCase{
		testResultRepo:   testResultRepo,
		analyticsService: analyticsService,
	}
}

// GetDashboardMetrics returns overall dashboard metrics
func (uc *AnalyticsUseCase) GetDashboardMetrics(filters *valueobjects.FilterCriteria) (*valueobjects.DashboardMetrics, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	metrics := uc.analyticsService.CalculateDashboardMetrics(testResults, filters)
	return &metrics, nil
}

// GetTestAnalytics returns detailed test analytics
func (uc *AnalyticsUseCase) GetTestAnalytics(filters *valueobjects.FilterCriteria) ([]valueobjects.TestAnalytics, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	analytics := uc.analyticsService.CalculateTestAnalytics(testResults, filters)
	return analytics, nil
}

// GetErrorCategoriesWithTests returns detailed error categories with associated failed tests
func (uc *AnalyticsUseCase) GetErrorCategoriesWithTests(filters *valueobjects.FilterCriteria) ([]valueobjects.ErrorCategoryDetail, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	errorCategories := uc.analyticsService.GetErrorCategoriesWithTests(testResults, filters)
	return errorCategories, nil
}

// GetTestRuns returns detailed test run data
func (uc *AnalyticsUseCase) GetTestRuns(filters *valueobjects.FilterCriteria) ([]valueobjects.TestRun, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	testRuns := uc.analyticsService.CalculateTestRuns(testResults, filters)
	return testRuns, nil
}

// GetChartData returns data formatted for various chart types
func (uc *AnalyticsUseCase) GetChartData(chartType string, filters *valueobjects.FilterCriteria) (*valueobjects.ChartData, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	switch chartType {
	case "pass-fail-trend":
		return uc.getPassFailTrendData(testResults, filters)
	case "test-duration":
		return uc.getTestDurationData(testResults, filters)
	case "status-distribution":
		return uc.getStatusDistributionData(testResults, filters)
	case "project-breakdown":
		return uc.getProjectBreakdownData(testResults, filters)
	case "error-categories":
		return uc.getErrorCategoriesData(testResults, filters)
	default:
		return uc.getPassFailTrendData(testResults, filters)
	}
}

// GetWordCloudData returns data for word cloud visualization
func (uc *AnalyticsUseCase) GetWordCloudData(filters *valueobjects.FilterCriteria) ([]valueobjects.WordCloudData, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	return uc.generateWordCloudData(testResults, filters), nil
}

// GetSunburstData returns hierarchical data for sunburst chart
func (uc *AnalyticsUseCase) GetSunburstData(filters *valueobjects.FilterCriteria) (*valueobjects.SunburstData, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	return uc.generateSunburstData(testResults, filters), nil
}

// GetBubbleChartData returns data for bubble chart visualization
func (uc *AnalyticsUseCase) GetBubbleChartData(filters *valueobjects.FilterCriteria) ([]valueobjects.BubbleChartData, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	return uc.generateBubbleChartData(testResults, filters), nil
}

// GetHeatmapData returns data for heatmap visualization
func (uc *AnalyticsUseCase) GetHeatmapData(filters *valueobjects.FilterCriteria) ([]valueobjects.HeatmapData, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	return uc.generateHeatmapData(testResults, filters), nil
}

// GetFilterOptions returns available filter options based on actual test data
func (uc *AnalyticsUseCase) GetFilterOptions() (map[string]any, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	return uc.extractFilterOptions(testResults), nil
}

// extractFilterOptions extracts all available filter options from test data
func (uc *AnalyticsUseCase) extractFilterOptions(testResults []entities.TestResult) map[string]any {
	statusSet := make(map[string]bool)
	projectSet := make(map[string]bool)
	prioritySet := make(map[string]bool)
	tagSet := make(map[string]bool)

	// Extract options from all test results
	for _, result := range testResults {
		uc.extractOptionsFromSuites(result.Suites, statusSet, projectSet, prioritySet, tagSet)
	}

	// Convert sets to slices
	statuses := []string{"passed", "failed", "skipped", "flaky"} // Always include these core statuses

	var projects []string
	for project := range projectSet {
		if project != "" {
			projects = append(projects, project)
		}
	}

	var priorities []string
	for priority := range prioritySet {
		if priority != "" {
			priorities = append(priorities, priority)
		}
	}

	var tags []string
	for tag := range tagSet {
		if tag != "" {
			tags = append(tags, tag)
		}
	}

	return map[string]any{
		"status":      statuses,
		"projectName": projects,
		"priority":    priorities,
		"tags":        tags,
	}
}

// extractOptionsFromSuites recursively extracts filter options from suites
func (uc *AnalyticsUseCase) extractOptionsFromSuites(suites []entities.Suite, statusSet, projectSet, prioritySet, tagSet map[string]bool) {
	for _, suite := range suites {
		// Skip coverage-setup & coverage-teardown as per requirements
		if strings.Contains(suite.Title, "coverage-setup") || strings.Contains(suite.Title, "coverage-teardown") {
			continue
		}

		// Extract from specs in this suite
		for _, spec := range suite.Specs {
			// Extract tags from specs.tags
			for _, tag := range spec.Tags {
				tagSet[tag] = true
			}

			// Extract from tests
			for _, test := range spec.Tests {
				// Exclude coverage-setup and coverage-teardown projects
				if test.ProjectName != "coverage-setup" && test.ProjectName != "coverage-teardown" {
					projectSet[test.ProjectName] = true

					for _, result := range test.Results {
						// Extract status
						statusSet[result.Status] = true

						// Check for flaky tests (passed after retry)
						if result.Retry > 0 && result.Status == "passed" {
							statusSet["flaky"] = true
						}

						// Extract priorities from annotations
						for _, annotation := range result.Annotations {
							if strings.HasPrefix(annotation.Description, "P") && len(annotation.Description) == 2 {
								prioritySet[annotation.Description] = true
							}
						}
					}
				}
			}
		}

		// Recursively process nested suites
		uc.extractOptionsFromSuites(suite.Suites, statusSet, projectSet, prioritySet, tagSet)
	}
}

// GetAvailableFiles returns list of available test result files
func (uc *AnalyticsUseCase) GetAvailableFiles() ([]string, error) {
	return uc.testResultRepo.GetTestResultFiles()
}

// DeleteTestResultFile deletes a test result file
func (uc *AnalyticsUseCase) DeleteTestResultFile(fileName string) error {
	return uc.testResultRepo.DeleteTestResult(fileName)
}

// SaveUploadedTestResult saves an uploaded test result
func (uc *AnalyticsUseCase) SaveUploadedTestResult(result *entities.TestResult, fileName string) error {
	return uc.testResultRepo.SaveTestResult(result, fileName)
}

// ProcessTestResult processes a test result from S3 and saves it
func (uc *AnalyticsUseCase) ProcessTestResult(result *entities.TestResult, fileName string) error {
	return uc.testResultRepo.SaveTestResult(result, fileName)
}

// GetAllTestResults returns all test results for chatbot analysis
func (uc *AnalyticsUseCase) GetAllTestResults() ([]entities.TestResult, error) {
	return uc.testResultRepo.LoadAllTestResults()
}

// GetAvailableDates returns available test run dates from the last 5 days
func (uc *AnalyticsUseCase) GetAvailableDates() ([]string, error) {
	testResults, err := uc.testResultRepo.LoadAllTestResults()
	if err != nil {
		return nil, err
	}

	return uc.analyticsService.GetAvailableDates(testResults), nil
}

// Helper methods for chart data generation
func (uc *AnalyticsUseCase) getPassFailTrendData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*valueobjects.ChartData, error) {
	metrics := uc.analyticsService.CalculateDashboardMetrics(testResults, filters)
	
	labels := make([]string, len(metrics.TrendData))
	passRates := make([]float64, len(metrics.TrendData))
	failRates := make([]float64, len(metrics.TrendData))

	for i, trend := range metrics.TrendData {
		labels[i] = trend.Date.Format("2006-01-02")
		passRates[i] = trend.PassRate
		failRates[i] = trend.FailRate
	}

	return &valueobjects.ChartData{
		Type:   "line",
		Title:  "Pass/Fail Rate Trend",
		Labels: labels,
		Datasets: []valueobjects.Dataset{
			{
				Label:       "Pass Rate (%)",
				Data:        passRates,
				BorderColor: []string{"#10B981"},
				Fill:        false,
			},
			{
				Label:       "Fail Rate (%)",
				Data:        failRates,
				BorderColor: []string{"#EF4444"},
				Fill:        false,
			},
		},
	}, nil
}

func (uc *AnalyticsUseCase) getTestDurationData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*valueobjects.ChartData, error) {
	analytics := uc.analyticsService.CalculateTestAnalytics(testResults, filters)
	
	// Get top 10 slowest tests
	if len(analytics) > 10 {
		analytics = analytics[:10]
	}

	labels := make([]string, len(analytics))
	durations := make([]float64, len(analytics))

	for i, test := range analytics {
		labels[i] = test.TestName
		durations[i] = test.AverageDuration / 1000 // Convert to seconds
	}

	return &valueobjects.ChartData{
		Type:   "bar",
		Title:  "Test Duration Analysis",
		Labels: labels,
		Datasets: []valueobjects.Dataset{
			{
				Label:           "Average Duration (seconds)",
				Data:            durations,
				BackgroundColor: []string{"#3B82F6"},
			},
		},
	}, nil
}

func (uc *AnalyticsUseCase) getStatusDistributionData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*valueobjects.ChartData, error) {
	metrics := uc.analyticsService.CalculateDashboardMetrics(testResults, filters)

	// Use the corrected metrics that traverse the full object hierarchy
	labels := []string{"Passed", "Failed", "Skipped"}
	data := []float64{
		float64(metrics.PassedTests), // This now includes 'expected' status as 'passed'
		float64(metrics.FailedTests), // This now includes 'unexpected' and 'timeout' as 'failed'
		float64(metrics.SkippedTests),
	}
	colors := []string{"#10B981", "#EF4444", "#F59E0B"}

	// Add flaky tests if they exist - flaky tests are now properly counted
	if flakyCount, exists := metrics.TestsByStatus["flaky"]; exists && flakyCount > 0 {
		labels = append(labels, "Flaky")
		data = append(data, float64(flakyCount))
		colors = append(colors, "#8B5CF6") // Purple for flaky
	}

	return &valueobjects.ChartData{
		Type:   "doughnut",
		Title:  "Test Status Distribution",
		Labels: labels,
		Datasets: []valueobjects.Dataset{
			{
				Label:           "Test Count",
				Data:            data,
				BackgroundColor: colors,
			},
		},
	}, nil
}

func (uc *AnalyticsUseCase) getProjectBreakdownData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*valueobjects.ChartData, error) {
	metrics := uc.analyticsService.CalculateDashboardMetrics(testResults, filters)
	
	labels := make([]string, 0, len(metrics.TestsByProject))
	data := make([]float64, 0, len(metrics.TestsByProject))

	for project, count := range metrics.TestsByProject {
		labels = append(labels, project)
		data = append(data, float64(count))
	}

	return &valueobjects.ChartData{
		Type:   "bar",
		Title:  "Tests by Project",
		Labels: labels,
		Datasets: []valueobjects.Dataset{
			{
				Label:           "Test Count",
				Data:            data,
				BackgroundColor: []string{"#8B5CF6"},
			},
		},
	}, nil
}

func (uc *AnalyticsUseCase) getErrorCategoriesData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*valueobjects.ChartData, error) {
	metrics := uc.analyticsService.CalculateDashboardMetrics(testResults, filters)

	labels := make([]string, 0, len(metrics.ErrorCategories))
	data := make([]float64, 0, len(metrics.ErrorCategories))

	for category, count := range metrics.ErrorCategories {
		labels = append(labels, category)
		data = append(data, float64(count))
	}

	return &valueobjects.ChartData{
		Type:   "pie",
		Title:  "Error Categories",
		Labels: labels,
		Datasets: []valueobjects.Dataset{
			{
				Label:           "Error Count",
				Data:            data,
				BackgroundColor: []string{"#EF4444", "#F59E0B", "#8B5CF6", "#06B6D4", "#84CC16"},
			},
		},
	}, nil
}

// Additional helper methods for advanced visualizations
func (uc *AnalyticsUseCase) generateWordCloudData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) []valueobjects.WordCloudData {
	wordCount := make(map[string]int)

	for _, result := range testResults {
		uc.extractWordsFromSuites(result.Suites, wordCount, filters)
	}

	var wordCloudData []valueobjects.WordCloudData
	for word, count := range wordCount {
		if count > 1 { // Only include words that appear more than once
			wordCloudData = append(wordCloudData, valueobjects.WordCloudData{
				Text:  word,
				Value: count,
				Color: uc.getColorForFrequency(count),
			})
		}
	}

	return wordCloudData
}

func (uc *AnalyticsUseCase) generateSunburstData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) *valueobjects.SunburstData {
	root := &valueobjects.SunburstData{
		Name:     "Test Results",
		Children: []valueobjects.SunburstData{},
	}

	projectMap := make(map[string]*valueobjects.SunburstData)

	for _, result := range testResults {
		uc.buildSunburstFromSuites(result.Suites, projectMap, filters)
	}

	for _, projectData := range projectMap {
		root.Children = append(root.Children, *projectData)
		root.Value += projectData.Value
	}

	return root
}

func (uc *AnalyticsUseCase) generateBubbleChartData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) []valueobjects.BubbleChartData {
	analytics := uc.analyticsService.CalculateTestAnalytics(testResults, filters)

	var bubbleData []valueobjects.BubbleChartData
	for _, test := range analytics {
		if test.TotalRuns > 0 {
			bubbleData = append(bubbleData, valueobjects.BubbleChartData{
				X:     test.AverageDuration / 1000, // Duration in seconds
				Y:     test.PassRate,
				R:     float64(test.TotalRuns) * 2, // Bubble size based on total runs
				Label: test.TestName,
				Color: uc.getColorForPassRate(test.PassRate),
			})
		}
	}

	return bubbleData
}

func (uc *AnalyticsUseCase) generateHeatmapData(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) []valueobjects.HeatmapData {
	analytics := uc.analyticsService.CalculateTestAnalytics(testResults, filters)

	var heatmapData []valueobjects.HeatmapData
	for _, test := range analytics {
		heatmapData = append(heatmapData, valueobjects.HeatmapData{
			X:     test.FilePath,
			Y:     test.TestName,
			Value: test.PassRate,
			Color: uc.getColorForPassRate(test.PassRate),
		})
	}

	return heatmapData
}

// Helper methods for visualization data processing
func (uc *AnalyticsUseCase) extractWordsFromSuites(suites []entities.Suite, wordCount map[string]int, filters *valueobjects.FilterCriteria) {
	for _, suite := range suites {
		for _, spec := range suite.Specs {
			for _, test := range spec.Tests {
				for _, result := range test.Results {
					if result.Error != nil {
						words := uc.extractWordsFromError(result.Error.Message)
						for _, word := range words {
							wordCount[word]++
						}
					}
				}
			}
		}
		uc.extractWordsFromSuites(suite.Suites, wordCount, filters)
	}
}

func (uc *AnalyticsUseCase) extractWordsFromError(errorMessage string) []string {
	if errorMessage == "" {
		return []string{}
	}

	// Convert to lowercase for processing
	message := strings.ToLower(errorMessage)

	// Define common stop words to filter out
	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true,
		"in": true, "on": true, "at": true, "to": true, "for": true, "of": true,
		"with": true, "by": true, "is": true, "was": true, "are": true, "were": true,
		"be": true, "been": true, "have": true, "has": true, "had": true, "do": true,
		"does": true, "did": true, "will": true, "would": true, "could": true,
		"should": true, "may": true, "might": true, "can": true, "cannot": true,
		"not": true, "no": true, "this": true, "that": true, "these": true, "those": true,
		"i": true, "you": true, "he": true, "she": true, "it": true, "we": true, "they": true,
		"me": true, "him": true, "her": true, "us": true, "them": true, "my": true, "your": true,
		"his": true, "its": true, "our": true, "their": true, "from": true, "up": true,
		"about": true, "into": true, "over": true, "after": true, "so": true, "if": true,
		"when": true, "where": true, "why": true, "how": true, "all": true, "any": true,
		"both": true, "each": true, "few": true, "more": true, "most": true, "other": true,
		"some": true, "such": true, "only": true, "own": true, "same": true, "than": true,
		"too": true, "very": true, "just": true, "now": true, "here": true, "there": true,
	}

	// Use regex to extract words (alphanumeric sequences)
	re := regexp.MustCompile(`[a-zA-Z0-9_]+`)
	matches := re.FindAllString(message, -1)

	var words []string
	for _, word := range matches {
		// Filter out stop words and short words
		if len(word) >= 3 && !stopWords[word] {
			// Skip pure numbers unless they look like error codes
			if matched, _ := regexp.MatchString(`^\d+$`, word); matched {
				// Only include if it looks like an error code (3+ digits)
				if len(word) >= 3 {
					words = append(words, word)
				}
			} else {
				words = append(words, word)
			}
		}
	}

	return words
}

func (uc *AnalyticsUseCase) buildSunburstFromSuites(suites []entities.Suite, projectMap map[string]*valueobjects.SunburstData, filters *valueobjects.FilterCriteria) {
	for _, suite := range suites {
		for _, spec := range suite.Specs {
			for _, test := range spec.Tests {
				projectName := test.ProjectName
				if projectName == "" {
					projectName = "Unknown"
				}

				if projectMap[projectName] == nil {
					projectMap[projectName] = &valueobjects.SunburstData{
						Name:     projectName,
						Children: []valueobjects.SunburstData{},
					}
				}

				// Add test data to project
				projectMap[projectName].Value += float64(len(test.Results))
			}
		}
		uc.buildSunburstFromSuites(suite.Suites, projectMap, filters)
	}
}

func (uc *AnalyticsUseCase) getColorForFrequency(frequency int) string {
	if frequency > 10 {
		return "#EF4444" // Red for high frequency
	} else if frequency > 5 {
		return "#F59E0B" // Orange for medium frequency
	}
	return "#10B981" // Green for low frequency
}

func (uc *AnalyticsUseCase) getColorForPassRate(passRate float64) string {
	if passRate >= 90 {
		return "#10B981" // Green for high pass rate
	} else if passRate >= 70 {
		return "#F59E0B" // Orange for medium pass rate
	}
	return "#EF4444" // Red for low pass rate
}
