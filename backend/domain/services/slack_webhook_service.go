package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"time"
)

// SlackWebhookService handles Slack notifications via webhooks
type SlackWebhookService struct {
	webhookURL string
}

// Ensure SlackWebhookService implements SlackNotifier interface
var _ SlackNotifier = (*SlackWebhookService)(nil)

// WebhookMessage represents a Slack webhook message
type WebhookMessage struct {
	Text        string       `json:"text"`
	Username    string       `json:"username,omitempty"`
	IconEmoji   string       `json:"icon_emoji,omitempty"`
	Attachments []Attachment `json:"attachments,omitempty"`
}

// Attachment represents a Slack message attachment
type Attachment struct {
	Color      string  `json:"color,omitempty"`
	Title      string  `json:"title,omitempty"`
	Text       string  `json:"text,omitempty"`
	Fields     []Field `json:"fields,omitempty"`
	Footer     string  `json:"footer,omitempty"`
	Timestamp  int64   `json:"ts,omitempty"`
}

// Field represents a field in a Slack attachment
type Field struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}

// NewSlackWebhookService creates a new webhook-based Slack service
func NewSlackWebhookService(webhookURL string) *SlackWebhookService {
	return &SlackWebhookService{
		webhookURL: webhookURL,
	}
}

// SendTestReport sends a test report via webhook
func (s *SlackWebhookService) SendTestReport(req SlackNotificationRequest) error {
	message := s.createWebhookMessage(req)
	
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal webhook message: %w", err)
	}

	resp, err := http.Post(s.webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send webhook: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("webhook returned status %d", resp.StatusCode)
	}

	return nil
}

// createWebhookMessage creates a formatted webhook message
func (s *SlackWebhookService) createWebhookMessage(req SlackNotificationRequest) WebhookMessage {
	var timeRangeText string
	switch req.TimeRange {
	case "24h":
		timeRangeText = "Last 24 Hours"
	case "7d":
		timeRangeText = "Last 7 Days"
	case "30d":
		timeRangeText = "Last 30 Days"
	case "custom":
		timeRangeText = fmt.Sprintf("Custom Range (%s to %s)", req.StartDate, req.EndDate)
	default:
		timeRangeText = "All Time"
	}

	status := "🟢 HEALTHY"
	color := "good"
	if req.PassRate < 80 {
		status = "🔴 CRITICAL"
		color = "danger"
	} else if req.PassRate < 90 {
		status = "🟡 WARNING"
		color = "warning"
	}

	// Calculate percentages
	passPercentage := float64(req.PassedTests) / float64(req.TotalTests) * 100
	failPercentage := float64(req.FailedTests) / float64(req.TotalTests) * 100
	flakyPercentage := float64(req.FlakyTests) / float64(req.TotalTests) * 100

	// Create insights
	insights := s.generateInsights(req)

	attachment := Attachment{
		Color: color,
		Title: fmt.Sprintf("🚀 ZTB-E2E Test Report - %s", timeRangeText),
		Text:  fmt.Sprintf("*Status:* %s", status),
		Fields: []Field{
			{
				Title: "📊 Total Tests",
				Value: fmt.Sprintf("%d", req.TotalTests),
				Short: true,
			},
			{
				Title: "✅ Passed",
				Value: fmt.Sprintf("%d (%.1f%%)", req.PassedTests, passPercentage),
				Short: true,
			},
			{
				Title: "❌ Failed",
				Value: fmt.Sprintf("%d (%.1f%%)", req.FailedTests, failPercentage),
				Short: true,
			},
			{
				Title: "⚠️ Flaky",
				Value: fmt.Sprintf("%d (%.1f%%)", req.FlakyTests, flakyPercentage),
				Short: true,
			},
			{
				Title: "🎯 Pass Rate",
				Value: fmt.Sprintf("%.1f%%", req.PassRate),
				Short: true,
			},
		},
		Footer:    "ZTB-E2E Analytics Platform",
		Timestamp: time.Now().Unix(),
	}

	// Add insights if available
	if insights != "" {
		attachment.Fields = append(attachment.Fields, Field{
			Title: "💡 Insights",
			Value: insights,
			Short: false,
		})
	}

	return WebhookMessage{
		Text:        fmt.Sprintf("🚀 *ZTB-E2E Test Report* - %s", timeRangeText),
		Username:    "ZTB-E2E Analytics",
		IconEmoji:   ":test_tube:",
		Attachments: []Attachment{attachment},
	}
}

// generateInsights creates intelligent insights based on test metrics
func (s *SlackWebhookService) generateInsights(req SlackNotificationRequest) string {
	var insights []string

	if req.PassRate >= 95 {
		insights = append(insights, "🌟 Excellent test stability! Keep up the great work.")
	} else if req.PassRate >= 90 {
		insights = append(insights, "✨ Good test performance with room for minor improvements.")
	} else if req.PassRate >= 80 {
		insights = append(insights, "⚠️ Test stability needs attention. Consider investigating failing tests.")
	} else {
		insights = append(insights, "🚨 Critical test stability issues detected. Immediate action required.")
	}

	if req.FlakyTests > 0 {
		flakyPercentage := float64(req.FlakyTests) / float64(req.TotalTests) * 100
		if flakyPercentage > 10 {
			insights = append(insights, fmt.Sprintf("🔄 High flaky test rate (%.1f%%). Consider stabilizing these tests.", flakyPercentage))
		} else if flakyPercentage > 5 {
			insights = append(insights, fmt.Sprintf("🔄 Moderate flaky test rate (%.1f%%). Monitor for patterns.", flakyPercentage))
		}
	}

	if req.FailedTests > req.TotalTests/2 {
		insights = append(insights, "💥 More than half of tests are failing. Check for systemic issues.")
	}

	if len(insights) == 0 {
		return ""
	}

	return fmt.Sprintf("• %s", fmt.Sprintf("%s\n• ", insights[0]) + fmt.Sprintf("%s", insights[1:]))
}

// ValidateConfiguration validates the webhook URL
func (s *SlackWebhookService) ValidateConfiguration() error {
	if s.webhookURL == "" {
		return fmt.Errorf("webhook URL is empty")
	}

	// Test the webhook with a simple message
	testMessage := WebhookMessage{
		Text:      "🧪 Test connection from ZTB-E2E Analytics",
		Username:  "ZTB-E2E Analytics",
		IconEmoji: ":white_check_mark:",
	}

	jsonData, err := json.Marshal(testMessage)
	if err != nil {
		return fmt.Errorf("failed to marshal test message: %w", err)
	}

	resp, err := http.Post(s.webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to send test webhook: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("webhook test returned status %d", resp.StatusCode)
	}



	return nil
}
