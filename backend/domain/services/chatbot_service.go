package services

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"cbi-e2e-analytics/domain/entities"
	"cbi-e2e-analytics/domain/valueobjects"
	"cbi-e2e-analytics/infrastructure/mcp"
)

// ChatbotService provides AI-powered analysis of test data
type ChatbotService struct {
	analyticsService *AnalyticsService
	mcpService       *mcp.MCPService
}

// NewChatbotService creates a new chatbot service
func NewChatbotService(analyticsService *AnalyticsService, mcpService *mcp.MCPService) *ChatbotService {
	return &ChatbotService{
		analyticsService: analyticsService,
		mcpService:       mcpService,
	}
}

// ChatRequest represents a user query
type ChatRequest struct {
	Message   string                        `json:"message"`
	Context   map[string]interface{}        `json:"context,omitempty"`
	Filters   *valueobjects.FilterCriteria  `json:"filters,omitempty"`
}

// ChatResponse represents the chatbot response
type ChatResponse struct {
	Message     string                 `json:"message"`
	Data        interface{}            `json:"data,omitempty"`
	Suggestions []string               `json:"suggestions,omitempty"`
	ChartData   interface{}            `json:"chartData,omitempty"`
	Timestamp   time.Time              `json:"timestamp"`
}

// ProcessQuery analyzes the user query and provides intelligent responses
func (c *ChatbotService) ProcessQuery(ctx context.Context, request ChatRequest, testResults []entities.TestResult) (*ChatResponse, error) {
	query := strings.ToLower(strings.TrimSpace(request.Message))

	// Try MCP service first if available
	if c.mcpService != nil {
		mcpResponse, err := c.mcpService.ProcessQuery(ctx, request.Message, testResults)
		if err == nil && mcpResponse != "" {
			return &ChatResponse{
				Message:   mcpResponse,
				Timestamp: time.Now(),
				Suggestions: []string{
					"Tell me more about failing tests",
					"Show performance insights",
					"Compare project metrics",
					"Analyze test trends",
				},
			}, nil
		}
		// If MCP fails, fall back to built-in logic
	}

	// Apply filters if provided
	filters := request.Filters
	if filters == nil {
		filters = &valueobjects.FilterCriteria{}
	}

	// Use built-in query processing as fallback
	switch {
	case c.isCasualGreeting(query):
		return c.handleCasualGreeting(query)
	case c.isOverviewQuery(query):
		return c.handleOverviewQuery(testResults, filters)
	case c.isFailureAnalysisQuery(query):
		return c.handleFailureAnalysisQuery(testResults, filters)
	case c.isPerformanceQuery(query):
		return c.handlePerformanceQuery(testResults, filters)
	case c.isTrendQuery(query):
		return c.handleTrendQuery(testResults, filters)
	case c.isProjectQuery(query):
		return c.handleProjectQuery(testResults, filters)
	case c.isSpecificTestQuery(query):
		return c.handleSpecificTestQuery(query, testResults, filters)
	case c.isComparisonQuery(query):
		return c.handleComparisonQuery(testResults, filters)
	default:
		return c.handleGeneralQuery(query, testResults, filters)
	}
}

// Query type detection methods
func (c *ChatbotService) isOverviewQuery(query string) bool {
	keywords := []string{"overview", "summary", "dashboard", "metrics", "overall", "total"}
	return c.containsAnyKeyword(query, keywords)
}

func (c *ChatbotService) isFailureAnalysisQuery(query string) bool {
	keywords := []string{"fail", "error", "issue", "problem", "bug", "broken"}
	return c.containsAnyKeyword(query, keywords)
}

func (c *ChatbotService) isPerformanceQuery(query string) bool {
	keywords := []string{"slow", "performance", "duration", "time", "speed", "timeout"}
	return c.containsAnyKeyword(query, keywords)
}

func (c *ChatbotService) isTrendQuery(query string) bool {
	keywords := []string{"trend", "over time", "history", "improvement", "degradation", "pattern"}
	return c.containsAnyKeyword(query, keywords)
}

func (c *ChatbotService) isProjectQuery(query string) bool {
	keywords := []string{"project", "module", "component"}
	return c.containsAnyKeyword(query, keywords)
}

func (c *ChatbotService) isSpecificTestQuery(query string) bool {
	keywords := []string{"test", "spec", "case", "scenario"}
	return c.containsAnyKeyword(query, keywords) && (strings.Contains(query, "which") || strings.Contains(query, "what") || strings.Contains(query, "show me"))
}

func (c *ChatbotService) isComparisonQuery(query string) bool {
	keywords := []string{"compare", "vs", "versus", "difference", "better", "worse"}
	return c.containsAnyKeyword(query, keywords)
}

func (c *ChatbotService) isCasualGreeting(query string) bool {
	// Check for casual greetings and simple messages that don't require test analysis
	greetings := []string{"hi", "hey", "hello", "hiya", "sup", "yo", "good morning", "good afternoon", "good evening"}
	simpleMessages := []string{"thanks", "thank you", "ok", "okay", "cool", "nice", "great", "awesome", "perfect"}

	// Trim and check if the entire query is just a greeting or simple response
	trimmed := strings.TrimSpace(query)

	// Check exact matches for short greetings
	for _, greeting := range greetings {
		if trimmed == greeting || trimmed == greeting+"!" || trimmed == greeting+"." {
			return true
		}
	}

	// Check exact matches for simple responses
	for _, simple := range simpleMessages {
		if trimmed == simple || trimmed == simple+"!" || trimmed == simple+"." {
			return true
		}
	}

	// Check if it's a very short message (likely casual)
	words := strings.Fields(trimmed)
	if len(words) <= 2 && len(trimmed) <= 10 {
		// Check if it contains any greeting words
		for _, word := range words {
			for _, greeting := range greetings {
				if strings.Contains(strings.ToLower(word), greeting) {
					return true
				}
			}
		}
	}

	return false
}

func (c *ChatbotService) containsAnyKeyword(query string, keywords []string) bool {
	for _, keyword := range keywords {
		if strings.Contains(query, keyword) {
			return true
		}
	}
	return false
}

// Query handlers
func (c *ChatbotService) handleOverviewQuery(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	metrics := c.analyticsService.CalculateDashboardMetrics(testResults, filters)
	
	message := fmt.Sprintf(`📊 **Test Suite Overview**

**Overall Statistics:**
• Total Tests: %d
• Pass Rate: %.1f%%
• Fail Rate: %.1f%%
• Average Duration: %.2f seconds

**Test Distribution:**
• Passed: %d tests
• Failed: %d tests
• Skipped: %d tests

**Projects:** %d active projects
**Recent Trend:** %s`,
		metrics.TotalTests,
		metrics.OverallPassRate,
		metrics.OverallFailRate,
		metrics.AverageDuration/1000,
		metrics.PassedTests,
		metrics.FailedTests,
		metrics.SkippedTests,
		len(metrics.TestsByProject),
		c.getTrendDescription(metrics.TrendData))

	suggestions := []string{
		"Show me failing tests",
		"What are the slowest tests?",
		"Compare project performance",
		"Show test trends over time",
	}

	return &ChatResponse{
		Message:     message,
		Data:        metrics,
		Suggestions: suggestions,
		Timestamp:   time.Now(),
	}, nil
}

func (c *ChatbotService) handleFailureAnalysisQuery(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	analytics := c.analyticsService.CalculateTestAnalytics(testResults, filters)
	
	// Find failing tests
	var failingTests []valueobjects.TestAnalytics
	for _, test := range analytics {
		if test.FailRate > 0 {
			failingTests = append(failingTests, test)
		}
	}

	// Sort by fail rate
	sort.Slice(failingTests, func(i, j int) bool {
		return failingTests[i].FailRate > failingTests[j].FailRate
	})

	message := fmt.Sprintf(`🔍 **Failure Analysis**

**Summary:**
• %d tests have failures
• Top failure rate: %.1f%%

**Most Problematic Tests:**`,
		len(failingTests),
		func() float64 {
			if len(failingTests) > 0 {
				return failingTests[0].FailRate
			}
			return 0
		}())

	// Add top 5 failing tests
	limit := 5
	if len(failingTests) < limit {
		limit = len(failingTests)
	}

	for i := 0; i < limit; i++ {
		test := failingTests[i]
		message += fmt.Sprintf("\n• %s (%.1f%% fail rate)", test.TestName, test.FailRate)
	}

	if len(failingTests) == 0 {
		message = "🎉 **Great News!** No failing tests found in the current dataset."
	}

	suggestions := []string{
		"Show error patterns",
		"Which projects have most failures?",
		"Show flaky tests",
		"Performance impact of failures",
	}

	return &ChatResponse{
		Message:     message,
		Data:        failingTests[:limit],
		Suggestions: suggestions,
		Timestamp:   time.Now(),
	}, nil
}

func (c *ChatbotService) handlePerformanceQuery(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	analytics := c.analyticsService.CalculateTestAnalytics(testResults, filters)
	
	// Sort by duration
	sort.Slice(analytics, func(i, j int) bool {
		return analytics[i].AverageDuration > analytics[j].AverageDuration
	})

	var totalDuration float64
	for _, test := range analytics {
		totalDuration += test.AverageDuration
	}

	avgDuration := totalDuration / float64(len(analytics))

	message := fmt.Sprintf(`⚡ **Performance Analysis**

**Duration Statistics:**
• Average test duration: %.2f seconds
• Slowest test: %.2f seconds
• Total execution time: %.2f minutes

**Slowest Tests:**`,
		avgDuration/1000,
		func() float64 {
			if len(analytics) > 0 {
				return analytics[0].AverageDuration / 1000
			}
			return 0
		}(),
		totalDuration/60000)

	// Add top 5 slowest tests
	limit := 5
	if len(analytics) < limit {
		limit = len(analytics)
	}

	for i := 0; i < limit; i++ {
		test := analytics[i]
		message += fmt.Sprintf("\n• %s (%.2fs)", test.TestName, test.AverageDuration/1000)
	}

	suggestions := []string{
		"Show timeout issues",
		"Compare project performance",
		"Performance trends",
		"Optimization recommendations",
	}

	return &ChatResponse{
		Message:     message,
		Data:        analytics[:limit],
		Suggestions: suggestions,
		Timestamp:   time.Now(),
	}, nil
}

func (c *ChatbotService) handleCasualGreeting(query string) (*ChatResponse, error) {
	message := `👋 **Hello! I'm here to help you with your test data analysis.**

I notice you sent a casual message. To provide meaningful insights, I need you to ask specific questions about your test data.

**Here are some examples of what you can ask:**
• "Show me an overview of my test results"
• "Which tests are failing the most?"
• "What are the slowest tests?"
• "How is my test performance trending?"
• "Compare performance across projects"
• "Show me flaky tests"

**What would you like to know about your tests?**`

	suggestions := []string{
		"Show test overview",
		"Which tests are failing?",
		"What are the slowest tests?",
		"Show test trends",
		"Compare projects",
		"Analyze flaky tests",
	}

	return &ChatResponse{
		Message:     message,
		Suggestions: suggestions,
		Timestamp:   time.Now(),
	}, nil
}

func (c *ChatbotService) handleGeneralQuery(query string, testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	message := `🤖 **I can help you analyze your test data!**

**What I can do:**
• Provide test suite overviews and metrics
• Analyze failing tests and error patterns
• Show performance insights and slow tests
• Compare projects and trends over time
• Answer specific questions about test cases

**Try asking:**
• "Show me an overview of my tests"
• "Which tests are failing?"
• "What are the slowest tests?"
• "Compare project performance"
• "Show me test trends"`

	suggestions := []string{
		"Show test overview",
		"Analyze failures",
		"Performance insights",
		"Project comparison",
		"Test trends",
	}

	return &ChatResponse{
		Message:     message,
		Suggestions: suggestions,
		Timestamp:   time.Now(),
	}, nil
}

// Helper methods
func (c *ChatbotService) getTrendDescription(trendData []valueobjects.TrendPoint) string {
	if len(trendData) < 2 {
		return "Insufficient data for trend analysis"
	}

	recent := trendData[len(trendData)-1]
	previous := trendData[len(trendData)-2]

	if recent.PassRate > previous.PassRate {
		return "Improving ↗️"
	} else if recent.PassRate < previous.PassRate {
		return "Declining ↘️"
	}
	return "Stable ➡️"
}

// Additional handler methods would be implemented here...
func (c *ChatbotService) handleTrendQuery(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	// Implementation for trend analysis
	return c.handleGeneralQuery("trends", testResults, filters)
}

func (c *ChatbotService) handleProjectQuery(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	// Implementation for project analysis
	return c.handleGeneralQuery("projects", testResults, filters)
}

func (c *ChatbotService) handleSpecificTestQuery(query string, testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	// Implementation for specific test queries
	return c.handleGeneralQuery("specific tests", testResults, filters)
}

func (c *ChatbotService) handleComparisonQuery(testResults []entities.TestResult, filters *valueobjects.FilterCriteria) (*ChatResponse, error) {
	// Implementation for comparison queries
	return c.handleGeneralQuery("comparison", testResults, filters)
}
