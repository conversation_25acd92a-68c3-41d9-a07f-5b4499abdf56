package services

import (
	"fmt"
	"strings"
	"time"

	"github.com/slack-go/slack"
)

// SlackNotifier interface for different Slack notification methods
type SlackNotifier interface {
	SendTestReport(req SlackNotificationRequest) error
	ValidateConfiguration() error
}

// SlackService handles Slack notifications
type SlackService struct {
	client     *slack.Client
	channel    string
	webhookURL string // For webhook-based notifications
}

// SlackNotificationRequest represents the request for sending Slack notifications
type SlackNotificationRequest struct {
	TimeRange   string `json:"timeRange"`
	StartDate   string `json:"startDate,omitempty"`
	EndDate     string `json:"endDate,omitempty"`
	TotalTests  int    `json:"totalTests"`
	PassedTests int    `json:"passedTests"`
	FailedTests int    `json:"failedTests"`
	FlakyTests  int    `json:"flakyTests"`
	PassRate    float64 `json:"passRate"`
}

// NewSlackService creates a new Slack service instance
func NewSlackService(token, channel string) *SlackService {
	client := slack.New(token)
	return &SlackService{
		client:  client,
		channel: channel,
	}
}

// SendTestReport sends a formatted test report to Slack
func (s *SlackService) SendTestReport(req SlackNotificationRequest) error {
	// First, test the authentication
	_, err := s.client.AuthTest()
	if err != nil {
		return fmt.Errorf("slack authentication failed: %w", err)
	}



	// Try simple text message first (fallback for limited permissions)
	message := s.formatSimpleTextMessage(req)

	_, _, err = s.client.PostMessage(
		s.channel,
		slack.MsgOptionText(message, false),
		slack.MsgOptionAsUser(true), // Use user token instead of bot
	)

	if err != nil {
		// If that fails, try with blocks (requires more permissions)
		blocks := s.createMessageBlocks(req)
		_, _, err = s.client.PostMessage(
			s.channel,
			slack.MsgOptionText(s.formatTestReportMessage(req), false),
			slack.MsgOptionBlocks(blocks...),
			slack.MsgOptionAsUser(true),
		)

		if err != nil {
			return fmt.Errorf("failed to post message to slack (tried both simple and blocks): %w", err)
		}
	}

	return nil
}

// formatTestReportMessage creates a formatted message for the test report
func (s *SlackService) formatTestReportMessage(req SlackNotificationRequest) string {
	var timeRangeText string
	switch req.TimeRange {
	case "24h":
		timeRangeText = "Last 24 Hours"
	case "7d":
		timeRangeText = "Last 7 Days"
	case "30d":
		timeRangeText = "Last 30 Days"
	case "custom":
		timeRangeText = fmt.Sprintf("Custom Range (%s to %s)", req.StartDate, req.EndDate)
	default:
		timeRangeText = "All Time"
	}

	status := "🟢 HEALTHY"
	if req.PassRate < 80 {
		status = "🔴 CRITICAL"
	} else if req.PassRate < 90 {
		status = "🟡 WARNING"
	}

	return fmt.Sprintf("🚀 *ZTB-E2E Test Report* - %s\n%s", timeRangeText, status)
}

// formatSimpleTextMessage creates a simple text message (fallback for limited permissions)
func (s *SlackService) formatSimpleTextMessage(req SlackNotificationRequest) string {
	var timeRangeText string
	switch req.TimeRange {
	case "24h":
		timeRangeText = "Last 24 Hours"
	case "7d":
		timeRangeText = "Last 7 Days"
	case "30d":
		timeRangeText = "Last 30 Days"
	case "custom":
		timeRangeText = fmt.Sprintf("Custom Range (%s to %s)", req.StartDate, req.EndDate)
	default:
		timeRangeText = "All Time"
	}

	status := "🟢 HEALTHY"
	if req.PassRate < 80 {
		status = "🔴 CRITICAL"
	} else if req.PassRate < 90 {
		status = "🟡 WARNING"
	}

	message := fmt.Sprintf(`🚀 *ZTB-E2E Test Report* - %s
%s

📊 *Test Metrics:*
• Total Tests: %d
• ✅ Passed: %d (%.1f%%)
• ❌ Failed: %d
• ⚠️ Flaky: %d
• 🎯 Pass Rate: %.1f%%

Generated on %s | ZTB-E2E Analytics Platform`,
		timeRangeText,
		status,
		req.TotalTests,
		req.PassedTests,
		float64(req.PassedTests)/float64(req.TotalTests)*100,
		req.FailedTests,
		req.FlakyTests,
		req.PassRate,
		time.Now().Format("Jan 2, 2006 at 3:04 PM"),
	)

	return message
}

// createMessageBlocks creates Slack message blocks for rich formatting
func (s *SlackService) createMessageBlocks(req SlackNotificationRequest) []slack.Block {
	var timeRangeText string
	switch req.TimeRange {
	case "24h":
		timeRangeText = "Last 24 Hours"
	case "7d":
		timeRangeText = "Last 7 Days"
	case "30d":
		timeRangeText = "Last 30 Days"
	case "custom":
		timeRangeText = fmt.Sprintf("Custom Range (%s to %s)", req.StartDate, req.EndDate)
	default:
		timeRangeText = "All Time"
	}

	status := "🟢 HEALTHY"
	if req.PassRate < 80 {
		status = "🔴 CRITICAL"
	} else if req.PassRate < 90 {
		status = "🟡 WARNING"
	}

	blocks := []slack.Block{
		// Header
		slack.NewHeaderBlock(
			slack.NewTextBlockObject("plain_text", "🚀 ZTB-E2E Test Report", false, false),
		),
		
		// Time range and status
		slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*Time Range:* %s\n*Status:* %s", timeRangeText, status), false, false),
			nil,
			nil,
		),
		
		// Divider
		slack.NewDividerBlock(),
		
		// Metrics section
		slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", "*📊 Test Metrics*", false, false),
			[]*slack.TextBlockObject{
				slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*Total Tests*\n%d", req.TotalTests), false, false),
				slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*✅ Passed*\n%d", req.PassedTests), false, false),
				slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*❌ Failed*\n%d", req.FailedTests), false, false),
				slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*⚠️ Flaky*\n%d", req.FlakyTests), false, false),
			},
			nil,
		),
		
		// Pass rate section
		slack.NewSectionBlock(
			slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*🎯 Pass Rate: %.1f%%*", req.PassRate), false, false),
			nil,
			nil,
		),
	}

	// Add insights section
	insights := s.generateInsights(req)
	if insights != "" {
		blocks = append(blocks, 
			slack.NewDividerBlock(),
			slack.NewSectionBlock(
				slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("*💡 Insights*\n%s", insights), false, false),
				nil,
				nil,
			),
		)
	}

	// Add footer
	blocks = append(blocks,
		slack.NewDividerBlock(),
		slack.NewContextBlock(
			"",
			slack.NewTextBlockObject("mrkdwn", fmt.Sprintf("Generated on %s | ZTB-E2E Analytics Platform", time.Now().Format("Jan 2, 2006 at 3:04 PM")), false, false),
		),
	)

	return blocks
}

// generateInsights creates intelligent insights based on test metrics
func (s *SlackService) generateInsights(req SlackNotificationRequest) string {
	var insights []string

	if req.PassRate >= 95 {
		insights = append(insights, "🌟 Excellent test stability! Keep up the great work.")
	} else if req.PassRate >= 90 {
		insights = append(insights, "✨ Good test performance with room for minor improvements.")
	} else if req.PassRate >= 80 {
		insights = append(insights, "⚠️ Test stability needs attention. Consider investigating failing tests.")
	} else {
		insights = append(insights, "🚨 Critical test stability issues detected. Immediate action required.")
	}

	if req.FlakyTests > 0 {
		flakyPercentage := float64(req.FlakyTests) / float64(req.TotalTests) * 100
		if flakyPercentage > 10 {
			insights = append(insights, fmt.Sprintf("🔄 High flaky test rate (%.1f%%). Consider stabilizing these tests.", flakyPercentage))
		} else if flakyPercentage > 5 {
			insights = append(insights, fmt.Sprintf("🔄 Moderate flaky test rate (%.1f%%). Monitor for patterns.", flakyPercentage))
		}
	}

	if req.FailedTests > req.TotalTests/2 {
		insights = append(insights, "💥 More than half of tests are failing. Check for systemic issues.")
	}

	if len(insights) == 0 {
		return ""
	}

	return strings.Join(insights, "\n")
}

// ValidateConfiguration validates the Slack configuration
func (s *SlackService) ValidateConfiguration() error {
	// Test authentication
	_, err := s.client.AuthTest()
	if err != nil {
		return fmt.Errorf("slack authentication failed - please check your token: %w", err)
	}

	// Check if the channel exists and bot has access
	_, err = s.client.GetConversationInfo(&slack.GetConversationInfoInput{
		ChannelID: s.channel,
	})
	if err != nil {
		return fmt.Errorf("cannot access channel '%s' - please check channel name and bot permissions: %w", s.channel, err)
	}



	return nil
}
