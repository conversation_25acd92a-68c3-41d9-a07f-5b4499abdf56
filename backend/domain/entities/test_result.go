package entities

import (
	"time"
)

// TestResult represents the root aggregate for Playwright test results
type TestResult struct {
	ID             string    `json:"id"`
	Config         Config    `json:"config"`
	Suites         []Suite   `json:"suites"`
	Errors         []string  `json:"errors"`
	Stats          Stats     `json:"stats"`
	FileName       string    `json:"fileName"`
	RunFolderName  string    `json:"runFolderName"`  // S3 run folder name for video/screenshot mapping
	LoadedAt       time.Time `json:"loadedAt"`
}

// Config represents the Playwright configuration
type Config struct {
	ConfigFile    string    `json:"configFile"`
	RootDir       string    `json:"rootDir"`
	Version       string    `json:"version"`
	Workers       int       `json:"workers"`
	Projects      []Project `json:"projects"`
	ForbidOnly    bool      `json:"forbidOnly"`
	FullyParallel bool      `json:"fullyParallel"`
}

// Project represents a Playwright project configuration
type Project struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	TestDir  string `json:"testDir"`
	Timeout  int    `json:"timeout"`
	Retries  int    `json:"retries"`
	OutputDir string `json:"outputDir"`
}

// Suite represents a test suite
type Suite struct {
	Title   string  `json:"title"`
	File    string  `json:"file"`
	Column  int     `json:"column"`
	Line    int     `json:"line"`
	Specs   []Spec  `json:"specs"`
	Suites  []Suite `json:"suites"`
}

// Spec represents a test specification
type Spec struct {
	Title string `json:"title"`
	OK    bool   `json:"ok"`
	Tags  []string `json:"tags"`
	Tests []Test `json:"tests"`
	ID    string `json:"id"`
	File  string `json:"file"`
	Line  int    `json:"line"`
	Column int   `json:"column"`
}

// Test represents an individual test execution
type Test struct {
	Timeout        int             `json:"timeout"`
	Annotations    []Annotation    `json:"annotations"`
	ExpectedStatus string          `json:"expectedStatus"`
	ProjectID      string          `json:"projectId"`
	ProjectName    string          `json:"projectName"`
	Results        []TestExecution `json:"results"`
	Status         string          `json:"status"`
}

// TestExecution represents the result of a test execution
type TestExecution struct {
	WorkerIndex    int          `json:"workerIndex"`
	ParallelIndex  int          `json:"parallelIndex"`
	Status         string       `json:"status"`
	Duration       int          `json:"duration"`
	Errors         []TestError  `json:"errors"`
	Stdout         []Output     `json:"stdout"`
	Stderr         []Output     `json:"stderr"`
	Retry          int          `json:"retry"`
	StartTime      time.Time    `json:"startTime"`
	Annotations    []Annotation `json:"annotations"`
	Attachments    []Attachment `json:"attachments"`
	Steps          []Step       `json:"steps"`
	Error          *TestError   `json:"error,omitempty"`
	ErrorLocation  *Location    `json:"errorLocation,omitempty"`
	ProjectName    string       `json:"projectName,omitempty"`
}

// Annotation represents test annotations like xray_test IDs
type Annotation struct {
	Type        string `json:"type"`
	Description string `json:"description"`
}

// TestError represents an error that occurred during test execution
type TestError struct {
	Message  string    `json:"message"`
	Stack    string    `json:"stack"`
	Location *Location `json:"location,omitempty"`
	Snippet  string    `json:"snippet,omitempty"`
}

// Output represents stdout/stderr output
type Output struct {
	Text string `json:"text"`
}

// Attachment represents test attachments like videos, logs
type Attachment struct {
	Name        string `json:"name"`
	ContentType string `json:"contentType"`
	Path        string `json:"path"`
}

// Step represents a test step
type Step struct {
	Title    string     `json:"title"`
	Duration int        `json:"duration"`
	Error    *TestError `json:"error,omitempty"`
}

// Location represents a file location
type Location struct {
	File   string `json:"file"`
	Line   int    `json:"line"`
	Column int    `json:"column"`
}

// Stats represents test execution statistics
type Stats struct {
	StartTime  time.Time `json:"startTime"`
	Duration   float64   `json:"duration"`
	Expected   int       `json:"expected"`
	Skipped    int       `json:"skipped"`
	Unexpected int       `json:"unexpected"`
	Flaky      int       `json:"flaky"`
}
