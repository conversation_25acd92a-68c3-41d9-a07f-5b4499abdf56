package valueobjects

import (
	"time"
)

// TestAnalytics represents aggregated analytics for a test
type TestAnalytics struct {
	SerialNumber    int               `json:"serialNumber"`
	TestName        string            `json:"testName"`
	FilePath        string            `json:"filePath"`
	PassRate        float64           `json:"passRate"`
	FailRate        float64           `json:"failRate"`
	FlakyRate       float64           `json:"flakyRate"`
	SkippedRate     float64           `json:"skippedRate"`
	TotalRuns       int               `json:"totalRuns"`
	TestCreatedDate time.Time         `json:"testCreatedDate"`
	TestUpdatedDate time.Time         `json:"testUpdatedDate"`
	AverageDuration float64           `json:"averageDuration"`
	RetryCount      int               `json:"retryCount"`
	ProjectName     string            `json:"projectName"`
	Tags            []string          `json:"tags"`
	ErrorMessages   []string          `json:"errorMessages"`
	LastStatus      string            `json:"lastStatus"`
	RunStatusMap    map[string]string `json:"-"` // Maps fileName to final status for that run (not exported to JSON)

	// S3-related fields for attachments
	VideoS3Key         string                   `json:"videoS3Key,omitempty"`
	VideoURL           string                   `json:"videoUrl,omitempty"`
	ScreenshotS3Keys   []string                 `json:"screenshotS3Keys,omitempty"`
	ScreenshotURLs     []string                 `json:"screenshotUrls,omitempty"`
	AllAttachmentS3Keys []map[string]string     `json:"allAttachmentS3Keys,omitempty"`  // All attachments with S3 keys
	Attachments        []TestRunAttachment      `json:"attachments,omitempty"`
}

// TestRun represents a single test execution run with detailed information
type TestRun struct {
	ID             string            `json:"id"`
	SerialNumber   int               `json:"serialNumber"`
	TestName       string            `json:"testName"`
	FilePath       string            `json:"filePath"`
	ProjectName    string            `json:"projectName"`
	Status         string            `json:"status"`
	Duration       int               `json:"duration"`
	StartTime      time.Time         `json:"startTime"`
	WorkerIndex    int               `json:"workerIndex"`
	ParallelIndex  int               `json:"parallelIndex"`
	Retry          int               `json:"retry"`
	Tags           []string          `json:"tags"`
	ExpectedStatus string            `json:"expectedStatus"`
	FileName       string            `json:"fileName"`
	Steps          []TestRunStep     `json:"steps"`
	Errors         []TestRunError    `json:"errors"`
	Stdout         []TestRunOutput   `json:"stdout"`
	Stderr         []TestRunOutput   `json:"stderr"`
	Attachments    []TestRunAttachment `json:"attachments"`
	Annotations    []TestRunAnnotation `json:"annotations"`

	// S3-related fields
	VideoS3Key          string                   `json:"videoS3Key,omitempty"`
	VideoURL            string                   `json:"videoUrl,omitempty"`
	ScreenshotS3Keys    []string                 `json:"screenshotS3Keys,omitempty"`
	ScreenshotURLs      []string                 `json:"screenshotUrls,omitempty"`
	AllAttachmentS3Keys []map[string]string      `json:"allAttachmentS3Keys,omitempty"`
}

// TestRunStep represents a step in a test execution
type TestRunStep struct {
	Title    string         `json:"title"`
	Duration int            `json:"duration"`
	Error    *TestRunError  `json:"error,omitempty"`
}

// TestRunError represents an error in test execution
type TestRunError struct {
	Message  string            `json:"message"`
	Stack    string            `json:"stack,omitempty"`
	Location *TestRunLocation  `json:"location,omitempty"`
	Snippet  string            `json:"snippet,omitempty"`
}

// TestRunOutput represents stdout/stderr output
type TestRunOutput struct {
	Text string `json:"text"`
}

// TestRunAttachment represents test attachments
type TestRunAttachment struct {
	Name        string `json:"name"`
	ContentType string `json:"contentType"`
	Path        string `json:"path"`
	S3Key       string `json:"s3Key,omitempty"`
	URL         string `json:"url,omitempty"`
}

// TestRunAnnotation represents test annotations
type TestRunAnnotation struct {
	Type        string `json:"type"`
	Description string `json:"description"`
}

// TestRunLocation represents a file location
type TestRunLocation struct {
	File   string `json:"file"`
	Line   int    `json:"line"`
	Column int    `json:"column"`
}

// ErrorCategoryDetail represents detailed error category information
type ErrorCategoryDetail struct {
	Category    string          `json:"category"`
	Count       int             `json:"count"`
	FailedTests []TestAnalytics `json:"failedTests"`
}

// DashboardMetrics represents overall dashboard metrics
type DashboardMetrics struct {
	TotalTests        int                    `json:"totalTests"`
	PassedTests       int                    `json:"passedTests"`
	FailedTests       int                    `json:"failedTests"`
	SkippedTests      int                    `json:"skippedTests"`
	OverallPassRate   float64                `json:"overallPassRate"`
	OverallFailRate   float64                `json:"overallFailRate"`
	OverallFlakyRate  float64                `json:"overallFlakyRate"`
	OverallSkipRate   float64                `json:"overallSkipRate"`
	AverageDuration   float64                `json:"averageDuration"`
	TotalDuration     float64                `json:"totalDuration"`
	TestsByProject    map[string]int         `json:"testsByProject"`
	TestsByStatus     map[string]int         `json:"testsByStatus"`
	TrendData         []TrendPoint           `json:"trendData"`
	ErrorCategories   map[string]int         `json:"errorCategories"`
	TopFailingTests   []TestAnalytics        `json:"topFailingTests"`
	SlowestTests      []TestAnalytics        `json:"slowestTests"`
	MostRetriedTests  []TestAnalytics        `json:"mostRetriedTests"`
	FilePathMetrics   map[string]FileMetrics `json:"filePathMetrics"`
}

// TrendPoint represents a point in time for trend analysis
type TrendPoint struct {
	Date      time.Time `json:"date"`
	PassRate  float64   `json:"passRate"`
	FailRate  float64   `json:"failRate"`
	TestCount int       `json:"testCount"`
	Duration  float64   `json:"duration"`
}

// FileMetrics represents metrics for a specific file path
type FileMetrics struct {
	FilePath    string  `json:"filePath"`
	TestCount   int     `json:"testCount"`
	PassRate    float64 `json:"passRate"`
	FailRate    float64 `json:"failRate"`
	AvgDuration float64 `json:"avgDuration"`
}

// FilterCriteria represents filter options for analytics
type FilterCriteria struct {
	StartDate   *time.Time `json:"startDate,omitempty"`
	EndDate     *time.Time `json:"endDate,omitempty"`
	Status      []string   `json:"status,omitempty"`
	ProjectName []string   `json:"projectName,omitempty"`
	FilePath    []string   `json:"filePath,omitempty"`
	Tags        []string   `json:"tags,omitempty"`
}

// ChartData represents data for various chart types
type ChartData struct {
	Labels   []string    `json:"labels"`
	Datasets []Dataset   `json:"datasets"`
	Type     string      `json:"type"`
	Title    string      `json:"title"`
}

// Dataset represents a dataset for charts
type Dataset struct {
	Label           string      `json:"label"`
	Data            []float64   `json:"data"`
	BackgroundColor []string    `json:"backgroundColor,omitempty"`
	BorderColor     []string    `json:"borderColor,omitempty"`
	Fill            bool        `json:"fill,omitempty"`
}

// WordCloudData represents data for word cloud visualization
type WordCloudData struct {
	Text  string  `json:"text"`
	Value int     `json:"value"`
	Color string  `json:"color,omitempty"`
}

// SunburstData represents hierarchical data for sunburst chart
type SunburstData struct {
	Name     string         `json:"name"`
	Value    float64        `json:"value"`
	Children []SunburstData `json:"children,omitempty"`
	Color    string         `json:"color,omitempty"`
}

// BubbleChartData represents data for bubble charts
type BubbleChartData struct {
	X      float64 `json:"x"`
	Y      float64 `json:"y"`
	R      float64 `json:"r"`
	Label  string  `json:"label"`
	Color  string  `json:"color,omitempty"`
}

// HeatmapData represents data for heatmap visualization
type HeatmapData struct {
	X     string  `json:"x"`
	Y     string  `json:"y"`
	Value float64 `json:"value"`
	Color string  `json:"color,omitempty"`
}
