/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", "Robot<PERSON>",
    "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-8 {
  margin-top: 2rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

/* Custom component styles */
.dashboard-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.metric-card {
  color: white;
  border-radius: 12px;
  padding: 6px;
  text-align: center;
  transition: all 0.3s ease;
}

/* Specific gradient backgrounds for each metric card type */
.metric-card-total {
  background: linear-gradient(
    135deg,
    #60a5fa 0%,
    #3b82f6 100%
  ); /* Light blue gradient */
}

.metric-card-pass {
  background: linear-gradient(
    135deg,
    #4ade80 0%,
    #22c55e 100%
  ); /* Light green gradient */
}

.metric-card-fail {
  background: linear-gradient(
    135deg,
    #f87171 0%,
    #ef4444 100%
  ); /* Light red gradient */
}

.metric-card-flaky {
  background: linear-gradient(
    135deg,
    #facc15 0%,
    #eab308 100%
  ); /* Light yellow gradient */
}

.metric-card-skipped {
  background: linear-gradient(
    135deg,
    #9ca3af 0%,
    #6b7280 100%
  ); /* Light gray gradient */
}

.metric-card-duration {
  background: linear-gradient(
    135deg,
    #a78bfa 0%,
    #8b5cf6 100%
  ); /* Light purple gradient */
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 1rem;
  opacity: 0.9;
  font-weight: 500;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.filter-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-area.dragover {
  border-color: #1890ff;
  background: #e6f7ff;
}

/* Status indicators */
.status-passed {
  color: #52c41a;
  font-weight: 600;
}

.status-failed {
  color: #ff4d4f;
  font-weight: 600;
}

.status-skipped {
  color: #faad14;
  font-weight: 600;
}

/* Loading states */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .metric-value {
    font-size: 1.75rem;
  }

  .chart-container {
    padding: 16px;
  }

  .filter-section {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .metric-value {
    font-size: 1.5rem;
  }

  .metric-card {
    padding: 12px;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Layout fixes */
body {
  margin: 0;
  padding: 0;
  background: #f5f5f5;
}

/* Header positioning - Always on top */
header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1001 !important;
  /* Background and border are now handled in the component */
}

/* Sidebar positioning - Below header */
.ant-layout-sider {
  position: fixed !important;
  top: 64px !important;
  left: 0 !important;
  height: calc(100vh - 64px) !important;
  z-index: 1000 !important;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15) !important;
}

/* Main content area */
.ant-layout-content {
  background: #f5f5f5 !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
}

.ant-table-thead > tr > th {
  background: #fafafa !important;
  font-weight: 600 !important;
}

.ant-card {
  border-radius: 12px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
}

.ant-btn {
  border-radius: 8px !important;
  font-weight: 500 !important;
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%) !important;
  border: none !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;
}
