import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { ConfigProvider } from "antd";
import { Toaster } from "react-hot-toast";
import App from "./App";
import "./index.css";

// Ant Design theme configuration
const theme = {
  token: {
    colorPrimary: "#1890ff",
    colorSuccess: "#52c41a",
    colorWarning: "#faad14",
    colorError: "#ff4d4f",
    colorInfo: "#1890ff",
    borderRadius: 8,
    fontFamily:
      'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  },
  components: {
    Layout: {
      headerBg: "#001529",
      siderBg: "#001529",
      triggerBg: "transparent", // Set to transparent to remove the background color
    },
    Menu: {
      darkItemBg: "#001529",
      darkSubMenuItemBg: "#000c17",
    },
    Card: {
      borderRadius: 12,
      boxShadow: "0 2px 8px rgba(0, 0, 0, 0.06)",
    },
    Button: {
      borderRadius: 8,
    },
    Table: {
      borderRadius: 8,
    },
  },
};

// Toast configuration
const toastOptions = {
  duration: 4000,
  position: "top-right" as const,
  style: {
    background: "#363636",
    color: "#fff",
    borderRadius: "8px",
    fontSize: "14px",
    fontWeight: "500",
  },
  success: {
    iconTheme: {
      primary: "#52c41a",
      secondary: "#fff",
    },
  },
  error: {
    iconTheme: {
      primary: "#ff4d4f",
      secondary: "#fff",
    },
  },
};

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <ConfigProvider theme={theme}>
        <App />
        <Toaster toastOptions={toastOptions} />
      </ConfigProvider>
    </BrowserRouter>
  </React.StrictMode>
);
