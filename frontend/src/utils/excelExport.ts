import * as XLSX from "xlsx";
import { TestAnalytics, TestRun, FilterCriteria } from "@/types";

// Determine test status based on pass/fail/flaky rates
const determineTestStatus = (test: TestAnalytics): string => {
  const { passRate, failRate, flakyRate, skippedRate } = test;

  // If there's any flaky rate from backend, it's flaky
  if (flakyRate > 0) {
    return "flaky";
  }

  // If both pass rate and fail rate are > 0%, it's flaky
  if (passRate > 0 && failRate > 0) {
    return "flaky";
  }

  // If only pass rate > 0%, it's passed
  if (passRate > 0 && failRate === 0) {
    return "passed";
  }

  // If only fail rate > 0%, it's failed
  if (failRate > 0 && passRate === 0) {
    return "failed";
  }

  // If only skipped rate > 0%, it's skipped
  if (skippedRate > 0 && passRate === 0 && failRate === 0) {
    return "skipped";
  }

  // Default fallback
  return "unknown";
};

// Get the effective pass rate (includes flaky tests that eventually passed)
const getEffectivePassRate = (test: TestAnalytics): number => {
  return test.passRate + test.flakyRate;
};

/**
 * Utility function to export test analytics data to Excel
 */
export const exportToExcel = (
  data: TestAnalytics[],
  filename: string = "test-analytics"
) => {
  try {
    // Prepare data for Excel export
    const excelData = data.map((test, index) => ({
      "S.No": index + 1,
      "Test Name": test.testName || "",
      "File Path": test.filePath || "",
      "Effective Pass Rate (%)": parseFloat(
        getEffectivePassRate(test).toFixed(1)
      ),
      "Pure Pass Rate (%)": parseFloat((test.passRate || 0).toFixed(1)),
      "Fail Rate (%)": parseFloat((test.failRate || 0).toFixed(1)),
      "Flaky Rate (%)": parseFloat((test.flakyRate || 0).toFixed(1)),
      "Skipped Rate (%)": parseFloat((test.skippedRate || 0).toFixed(1)),
      "Total Runs": test.totalRuns || 0,
      "Average Duration (ms)": test.averageDuration || 0,
      Project: test.projectName || "",
      Priority: test.priority || "-",
      Status: determineTestStatus(test)
        ? test.lastStatus.charAt(0).toUpperCase() + test.lastStatus.slice(1)
        : "",
      Tags: test.tags && Array.isArray(test.tags) ? test.tags.join(", ") : "",
      "Retry Count": test.retryCount || 0,
      "Error Messages":
        test.errorMessages && Array.isArray(test.errorMessages)
          ? test.errorMessages.join("; ")
          : "",
      "Created Date": test.testCreatedDate || "",
      "Updated Date": test.testUpdatedDate || "",
    }));

    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 8 }, // S.No
      { wch: 40 }, // Test Name
      { wch: 50 }, // File Path
      { wch: 12 }, // Pass Rate
      { wch: 12 }, // Fail Rate
      { wch: 12 }, // Flaky Rate
      { wch: 12 }, // Skipped Rate
      { wch: 12 }, // Total Runs
      { wch: 18 }, // Average Duration
      { wch: 15 }, // Project
      { wch: 10 }, // Priority
      { wch: 10 }, // Status
      { wch: 30 }, // Tags
      { wch: 12 }, // Retry Count
      { wch: 50 }, // Error Messages
      { wch: 20 }, // Created Date
      { wch: 20 }, // Updated Date
    ];
    worksheet["!cols"] = columnWidths;

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, "Test Analytics");

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const finalFilename = `${filename}_${timestamp}.xlsx`;

    // Write and download the file
    XLSX.writeFile(workbook, finalFilename);

    return {
      success: true,
      filename: finalFilename,
      recordCount: data.length,
    };
  } catch (error) {
    console.error("Error exporting to Excel:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

/**
 * Format duration from milliseconds to human readable format
 */
export const formatDurationForExcel = (duration: number): string => {
  const seconds = duration / 1000;
  if (seconds < 60) {
    return `${seconds.toFixed(1)}s`;
  } else {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  }
};

/**
 * Export filtered test analytics data with additional formatting options
 */
export const exportFilteredTestAnalytics = (
  data: TestAnalytics[],
  filters: {
    searchText?: string;
    appliedFilters?: Record<string, any>;
  },
  filename: string = "filtered-test-analytics"
) => {
  try {
    // Prepare data with additional metadata
    const excelData = data.map((test, index) => ({
      "S.No": index + 1,
      "Test Name": test.testName || "",
      "File Path": test.filePath || "",
      "Effective Pass Rate (%)": parseFloat(
        getEffectivePassRate(test).toFixed(1)
      ),
      "Pure Pass Rate (%)": parseFloat((test.passRate || 0).toFixed(1)),
      "Fail Rate (%)": parseFloat((test.failRate || 0).toFixed(1)),
      "Flaky Rate (%)": parseFloat((test.flakyRate || 0).toFixed(1)),
      "Skipped Rate (%)": parseFloat((test.skippedRate || 0).toFixed(1)),
      "Total Runs": test.totalRuns || 0,
      "Average Duration": formatDurationForExcel(test.averageDuration || 0),
      "Average Duration (ms)": test.averageDuration || 0,
      Project: test.projectName || "",
      Priority: test.priority || "-",
      Status: determineTestStatus(test)
        ? test.lastStatus.charAt(0).toUpperCase() + test.lastStatus.slice(1)
        : "",
      Tags: test.tags && Array.isArray(test.tags) ? test.tags.join(", ") : "",
      "Retry Count": test.retryCount || 0,
      "Error Messages":
        test.errorMessages && Array.isArray(test.errorMessages)
          ? test.errorMessages.join("; ")
          : "",
      "Created Date": test.testCreatedDate || "",
      "Updated Date": test.testUpdatedDate || "",
    }));

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Create main data worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const columnWidths = [
      { wch: 8 }, // S.No
      { wch: 40 }, // Test Name
      { wch: 50 }, // File Path
      { wch: 12 }, // Pass Rate
      { wch: 12 }, // Fail Rate
      { wch: 12 }, // Flaky Rate
      { wch: 12 }, // Skipped Rate
      { wch: 12 }, // Total Runs
      { wch: 18 }, // Average Duration
      { wch: 18 }, // Average Duration (ms)
      { wch: 15 }, // Project
      { wch: 10 }, // Priority
      { wch: 10 }, // Status
      { wch: 30 }, // Tags
      { wch: 12 }, // Retry Count
      { wch: 50 }, // Error Messages
      { wch: 20 }, // Created Date
      { wch: 20 }, // Updated Date
    ];
    worksheet["!cols"] = columnWidths;

    // Add main worksheet
    XLSX.utils.book_append_sheet(workbook, worksheet, "Test Analytics");

    // Create summary worksheet with filter information
    const summaryData = [
      ["Export Summary", ""],
      ["Export Date", new Date().toISOString()],
      ["Total Records", data.length],
      ["Search Text", filters.searchText || "None"],
      [
        "Applied Filters",
        JSON.stringify(filters.appliedFilters || {}, null, 2),
      ],
    ];

    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
    summaryWorksheet["!cols"] = [{ wch: 20 }, { wch: 50 }];
    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, "Export Info");

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const finalFilename = `${filename}_${timestamp}.xlsx`;

    // Write and download the file
    XLSX.writeFile(workbook, finalFilename);

    return {
      success: true,
      filename: finalFilename,
      recordCount: data.length,
    };
  } catch (error) {
    console.error("Error exporting filtered data to Excel:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};

/**
 * Export test runs data to Excel with detailed information
 */
export const exportFilteredTestRuns = (
  data: TestRun[],
  filters: FilterCriteria,
  filename: string = "test-runs"
) => {
  try {
    // Prepare data for Excel export
    const excelData = data.map((run, index) => ({
      "S.No": index + 1,
      "Test Name": run.testName || "",
      "File Path": run.filePath || "",
      Project: run.projectName || "",
      Status:
        run.status === "expected"
          ? "Passed"
          : run.status.charAt(0).toUpperCase() + run.status.slice(1),
      "Duration (ms)": run.duration || 0,
      Duration: formatDurationForExcel(run.duration || 0),
      "Start Time": run.startTime
        ? new Date(run.startTime).toLocaleString()
        : "",
      "Worker Index": run.workerIndex || 0,
      "Parallel Index": run.parallelIndex || 0,
      "Retry Count": run.retry || 0,
      "Expected Status": run.expectedStatus || "",
      "Steps Count": run.steps?.length || 0,
      "Errors Count": run.errors?.length || 0,
      "Attachments Count": run.attachments?.length || 0,
      Tags: run.tags && Array.isArray(run.tags) ? run.tags.join(", ") : "",
      Priority: run.priority || "-",
      "Source File": run.fileName || "",
      Steps:
        run.steps
          ?.map(
            (step) => `${step.title} (${formatDurationForExcel(step.duration)})`
          )
          .join("; ") || "",
      "Error Messages":
        run.errors?.map((error) => error.message).join("; ") || "",
      "Stdout Lines": run.stdout?.length || 0,
      "Stderr Lines": run.stderr?.length || 0,
      Annotations:
        run.annotations
          ?.map((ann) => `${ann.type}: ${ann.description}`)
          .join("; ") || "",
    }));

    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Create main data worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);

    // Set column widths for better readability
    const columnWidths = [
      { wch: 8 }, // S.No
      { wch: 40 }, // Test Name
      { wch: 50 }, // File Path
      { wch: 15 }, // Project
      { wch: 12 }, // Status
      { wch: 12 }, // Duration (ms)
      { wch: 15 }, // Duration
      { wch: 20 }, // Start Time
      { wch: 10 }, // Worker Index
      { wch: 12 }, // Parallel Index
      { wch: 10 }, // Retry Count
      { wch: 15 }, // Expected Status
      { wch: 12 }, // Steps Count
      { wch: 12 }, // Errors Count
      { wch: 15 }, // Attachments Count
      { wch: 30 }, // Tags
      { wch: 10 }, // Priority
      { wch: 30 }, // Source File
      { wch: 60 }, // Steps
      { wch: 60 }, // Error Messages
      { wch: 12 }, // Stdout Lines
      { wch: 12 }, // Stderr Lines
      { wch: 60 }, // Annotations
    ];
    worksheet["!cols"] = columnWidths;

    // Add main worksheet
    XLSX.utils.book_append_sheet(workbook, worksheet, "Test Runs");

    // Create summary worksheet with filter information
    const summaryData = [
      ["Export Summary", ""],
      ["Export Date", new Date().toISOString()],
      ["Total Records", data.length],
      ["Applied Filters", JSON.stringify(filters || {}, null, 2)],
    ];

    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
    summaryWorksheet["!cols"] = [{ wch: 20 }, { wch: 50 }];
    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, "Export Info");

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const finalFilename = `${filename}_${timestamp}.xlsx`;

    // Write and download the file
    XLSX.writeFile(workbook, finalFilename);

    return {
      success: true,
      filename: finalFilename,
      recordCount: data.length,
    };
  } catch (error) {
    console.error("Error exporting test runs to Excel:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
};
