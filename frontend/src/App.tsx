import React, { useEffect, useState } from "react";
import { Routes, Route, Navigate, useLocation } from "react-router-dom";
import { Layout, Spin, Alert } from "antd";
import { motion, AnimatePresence } from "framer-motion";
import toast from "react-hot-toast";

// Components
import Sidebar from "@/components/layout/Sidebar";
import SimpleHeader from "@/components/layout/SimpleHeader";
import LoadingScreen from "@/components/common/LoadingScreen";
import FloatingChatbot from "@/components/chatbot/FloatingChatbot";

// Pages
import Dashboard from "@/pages/Dashboard";
import SimpleDashboard from "@/pages/SimpleDashboard";
import TestAnalytics from "@/pages/TestAnalytics";
import TestCases from "@/pages/TestCases";

import FileManagement from "@/pages/FileManagement";
import TrendsInsights from "@/pages/TrendsInsights";
import S3Configuration from "@/pages/S3Configuration";

// Services
import apiService from "@/services/api";

const { Content } = Layout;

const App: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [connectionError, setConnectionError] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const location = useLocation();

  // Get page title based on current route
  const getPageTitle = () => {
    switch (location.pathname) {
      case "/dashboard":
        return "Dashboard";
      case "/analytics":
        return "Test Analytics";
      case "/test-cases":
        return "Test Cases";

      case "/trends":
        return "Trends & Insights";
      case "/files":
        return "File Management";
      case "/homepage":
        return "Zero Trust Browser";
      case "/s3-config":
        return "S3 Integration";
      default:
        return "";
    }
  };

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Test API connection
      const isConnected = await apiService.testConnection();

      if (!isConnected) {
        setConnectionError(true);
        toast.error("Unable to connect to the backend API");
      } else {
        //toast.success("Connected to CBI-E2E Analytics API");
      }
    } catch (error) {
      console.error("App initialization error:", error);
      setConnectionError(true);
    } finally {
      // Reduce loading time for faster startup
      setTimeout(() => {
        setLoading(false);
      }, 500);
    }
  };

  const handleRetryConnection = async () => {
    setLoading(true);
    setConnectionError(false);
    await initializeApp();
  };

  if (loading) {
    return <LoadingScreen />;
  }

  if (connectionError) {
    return (
      <div className="min-h-screen flex-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center p-8"
        >
          <Alert
            message="Connection Error"
            description="Unable to connect to the backend API. Please ensure the server is running on port 8080."
            type="error"
            showIcon
            action={
              <button
                onClick={handleRetryConnection}
                className="ant-btn ant-btn-primary"
              >
                Retry Connection
              </button>
            }
          />
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header - Fixed at top */}
      <SimpleHeader
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        pageTitle={getPageTitle()}
      />

      {/* Sidebar - Fixed on left */}
      <Sidebar collapsed={sidebarCollapsed} onCollapse={setSidebarCollapsed} />

      {/* Main Content Area */}
      <main
        className="transition-all duration-300 p-6"
        style={{
          marginLeft: sidebarCollapsed ? 80 : 256,
          marginTop: 64,
          minHeight: "calc(100vh - 64px)",
        }}
      >
        <AnimatePresence mode="wait">
          <motion.div
            key={location.pathname}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Routes>
              <Route path="/" element={<Navigate to="/homepage" replace />} />
              <Route path="/homepage" element={<SimpleDashboard />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/analytics" element={<TestAnalytics />} />
              <Route path="/test-cases" element={<TestCases />} />

              <Route path="/files" element={<FileManagement />} />
              <Route path="/trends" element={<TrendsInsights />} />
              <Route path="/s3-config" element={<S3Configuration />} />
              <Route path="*" element={<Navigate to="/homepage" replace />} />
            </Routes>
          </motion.div>
        </AnimatePresence>
      </main>

      {/* Floating Chatbot */}
      <FloatingChatbot />
    </div>
  );
};

export default App;
