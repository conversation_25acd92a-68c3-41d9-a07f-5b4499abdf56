import React from "react";
import { Doughnut } from "react-chartjs-2";
import { Chart as ChartJ<PERSON>, ArcElement, Tooltip, Legend } from "chart.js";
import { Spin } from "antd";

// Register Chart.js components
ChartJS.register(ArcElement, Tooltip, Legend);

// Custom plugin to display data labels on chart segments
const dataLabelsPlugin = {
  id: "dataLabels",
  afterDraw: (chart: any) => {
    const ctx = chart.ctx;
    const meta = chart.getDatasetMeta(0);
    const dataset = chart.data.datasets[0];
    const total = dataset.data.reduce(
      (sum: number, value: number) => sum + value,
      0
    );

    if (total === 0) return;

    meta.data.forEach((element: any, index: number) => {
      const value = dataset.data[index];
      const percentage = ((value / total) * 100).toFixed(1);

      // Get the center point of the arc
      const { x, y } = element.tooltipPosition();

      // Set text style
      ctx.save();
      ctx.font = "bold 12px Arial";
      ctx.fillStyle = "#ffffff";
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";

      // Add text shadow for better visibility
      ctx.shadowColor = "rgba(0, 0, 0, 0.7)";
      ctx.shadowBlur = 3;
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;

      // Display value and percentage
      const text = `${value}\n(${percentage}%)`;
      const lines = text.split("\n");

      // Draw each line
      lines.forEach((line, lineIndex) => {
        const yOffset = (lineIndex - (lines.length - 1) / 2) * 14;
        ctx.fillText(line, x, y + yOffset);
      });

      ctx.restore();
    });
  },
};

interface StatusDistributionChartProps {
  data: {
    passed: number;
    failed: number;
    skipped: number;
    flaky?: number; // Add flaky tests support
  };
  loading?: boolean;
  height?: number;
}

const StatusDistributionChart: React.FC<StatusDistributionChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  const flakyCount = data.flaky || 0;
  const total = data.passed + data.failed + data.skipped + flakyCount;

  if (total === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No test data available
      </div>
    );
  }

  // Prepare chart data including flaky tests
  const labels = ["Passed", "Failed", "Skipped"];
  const chartValues = [data.passed, data.failed, data.skipped];
  const backgroundColors = [
    "#10B981", // Green for passed
    "#EF4444", // Red for failed
    "#F59E0B", // Orange for skipped
  ];
  const borderColors = ["#059669", "#DC2626", "#D97706"];
  const hoverColors = ["#34D399", "#F87171", "#FBBF24"];

  // Add flaky tests if they exist
  if (flakyCount > 0) {
    labels.push("Flaky");
    chartValues.push(flakyCount);
    backgroundColors.push("#8B5CF6"); // Purple for flaky
    borderColors.push("#7C3AED");
    hoverColors.push("#A78BFA");
  }

  const chartData = {
    labels,
    datasets: [
      {
        data: chartValues,
        backgroundColor: backgroundColors,
        borderColor: borderColors,
        borderWidth: 2,
        hoverBackgroundColor: hoverColors,
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            weight: "bold" as const,
          },
          generateLabels: (chart: any) => {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const value = data.datasets[0].data[i];
                const percentage = ((value / total) * 100).toFixed(1);
                return {
                  text: `${label}: ${value} (${percentage}%)`,
                  fillStyle: data.datasets[0].backgroundColor[i],
                  strokeStyle: data.datasets[0].borderColor[i],
                  lineWidth: data.datasets[0].borderWidth,
                  pointStyle: "circle",
                };
              });
            }
            return [];
          },
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "#e5e7eb",
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          label: function (context: any) {
            const label = context.label || "";
            const value = context.parsed;
            const percentage = ((value / total) * 100).toFixed(1);
            return `${label}: ${value} tests (${percentage}%)`;
          },
        },
      },
    },
    cutout: "60%",
    elements: {
      arc: {
        borderWidth: 2,
      },
    },
  };

  return (
    <div style={{ height: height }} className="relative">
      <Doughnut
        data={chartData}
        options={options}
        plugins={[dataLabelsPlugin]}
      />
    </div>
  );
};

export default StatusDistributionChart;
