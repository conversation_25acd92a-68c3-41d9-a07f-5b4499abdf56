import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Spin } from 'antd';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ProjectBreakdownChartProps {
  data: Record<string, number>;
  loading?: boolean;
  height?: number;
}

const ProjectBreakdownChart: React.FC<ProjectBreakdownChartProps> = ({ 
  data, 
  loading = false, 
  height = 300 
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  const projects = Object.keys(data);
  const values = Object.values(data);

  if (projects.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No project data available
      </div>
    );
  }

  // Generate colors for each project
  const colors = [
    '#3B82F6', // Blue
    '#10B981', // Green
    '#F59E0B', // Orange
    '#EF4444', // Red
    '#8B5CF6', // Purple
    '#06B6D4', // Cyan
    '#84CC16', // Lime
    '#F97316', // Orange-600
  ];

  const chartData = {
    labels: projects,
    datasets: [
      {
        label: 'Number of Tests',
        data: values,
        backgroundColor: projects.map((_, index) => colors[index % colors.length]),
        borderColor: projects.map((_, index) => colors[index % colors.length]),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
        hoverBackgroundColor: projects.map((_, index) => {
          const color = colors[index % colors.length];
          // Make hover color slightly lighter
          return color + '80'; // Add transparency
        }),
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false, // Hide legend for bar chart
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: function(context: any) {
            return `Project: ${context[0].label}`;
          },
          label: function(context: any) {
            const value = context.parsed.y;
            const total = values.reduce((sum, val) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `Tests: ${value} (${percentage}%)`;
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 11,
          },
          color: '#6B7280',
          maxRotation: 45,
          minRotation: 0,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11,
          },
          color: '#6B7280',
          precision: 0, // Show whole numbers only
        },
      },
    },
    elements: {
      bar: {
        borderWidth: 2,
      },
    },
  };

  return (
    <div style={{ height: height }}>
      <Bar data={chartData} options={options} />
    </div>
  );
};

export default ProjectBreakdownChart;
