import React from "react";
import { Spin } from "antd";
import { FileMetrics } from "@/types";

interface PerformanceHeatmapChartProps {
  data: Record<string, FileMetrics>;
  loading?: boolean;
  height?: number;
}

const PerformanceHeatmapChart: React.FC<PerformanceHeatmapChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  const fileMetrics = Object.values(data);

  if (fileMetrics.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No performance data available
      </div>
    );
  }

  // Sort by pass rate (ascending) to show problematic files first
  const sortedMetrics = fileMetrics
    .sort((a, b) => a.passRate - b.passRate)
    .slice(0, 20); // Show top 20 files

  const getColorIntensity = (passRate: number) => {
    // Red for low pass rates, green for high pass rates
    if (passRate >= 90) return "bg-green-500";
    if (passRate >= 80) return "bg-green-400";
    if (passRate >= 70) return "bg-yellow-400";
    if (passRate >= 60) return "bg-orange-400";
    if (passRate >= 50) return "bg-red-400";
    return "bg-red-500";
  };

  const getTextColor = (passRate: number) => {
    return passRate >= 70 ? "text-white" : "text-white";
  };

  const formatFilePath = (filePath: string) => {
    if (!filePath) return "Unknown";
    const parts = filePath.split("/");
    return parts.length > 2 ? `.../${parts.slice(-2).join("/")}` : filePath;
  };

  return (
    <div style={{ height: height }} className="overflow-auto">
      <div className="grid grid-cols-1 gap-2 p-4">
        {sortedMetrics.map((metric, index) => (
          <div
            key={metric.filePath}
            className={`p-3 rounded-lg transition-all duration-200 hover:scale-105 ${getColorIntensity(
              metric.passRate || 0
            )}`}
          >
            <div className="flex justify-between items-center">
              <div className={`flex-1 ${getTextColor(metric.passRate)}`}>
                <div
                  className="font-medium text-sm truncate"
                  title={metric.filePath}
                >
                  {formatFilePath(metric.filePath)}
                </div>
                <div className="text-xs opacity-90">
                  {metric.testCount || 0} tests •{" "}
                  {((metric.avgDuration || 0) / 1000).toFixed(1)}s avg
                </div>
              </div>
              <div
                className={`text-right ${getTextColor(metric.passRate || 0)}`}
              >
                <div className="text-lg font-bold">
                  {(metric.passRate || 0).toFixed(0)}%
                </div>
                <div className="text-xs opacity-90">pass rate</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Legend */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="text-xs font-medium text-gray-700 mb-2">
          Pass Rate Legend:
        </div>
        <div className="flex flex-wrap gap-2">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span className="text-xs">&lt;50%</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-400 rounded"></div>
            <span className="text-xs">50-60%</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-orange-400 rounded"></div>
            <span className="text-xs">60-70%</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-yellow-400 rounded"></div>
            <span className="text-xs">70-80%</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-400 rounded"></div>
            <span className="text-xs">80-90%</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-xs">90-100%</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceHeatmapChart;
