import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from "chart.js";
import { Spin } from "antd";
import { TrendPoint } from "@/types";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface TrendChartProps {
  data: TrendPoint[];
  loading?: boolean;
  height?: number;
  type?: "pass-fail" | "duration" | "retry";
}

const TrendChart: React.FC<TrendChartProps> = ({
  data,
  loading = false,
  height = 300,
  type = "pass-fail",
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No trend data available
      </div>
    );
  }

  const getChartData = () => {
    const labels = data.map((point) => {
      const date = new Date(point.date);
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    });

    switch (type) {
      case "duration":
        return {
          labels,
          datasets: [
            {
              label: "Average Duration (seconds)",
              data: data.map((point) => (point.duration || 0) / 1000),
              borderColor: "#3B82F6",
              backgroundColor: "rgba(59, 130, 246, 0.1)",
              borderWidth: 3,
              fill: true,
              tension: 0.4,
              pointBackgroundColor: "#3B82F6",
              pointBorderColor: "#ffffff",
              pointBorderWidth: 2,
              pointRadius: 6,
              pointHoverRadius: 8,
            },
          ],
        };

      case "retry":
        return {
          labels,
          datasets: [
            {
              label: "Average Retry Rate",
              data: data.map((point) => (point as any).retryRate || 0),
              borderColor: "#F59E0B",
              backgroundColor: "rgba(245, 158, 11, 0.1)",
              borderWidth: 3,
              fill: true,
              tension: 0.4,
              pointBackgroundColor: "#F59E0B",
              pointBorderColor: "#ffffff",
              pointBorderWidth: 2,
              pointRadius: 6,
              pointHoverRadius: 8,
            },
          ],
        };

      default: // pass-fail
        return {
          labels,
          datasets: [
            {
              label: "Pass Rate (%)",
              data: data.map((point) => point.passRate),
              borderColor: "#10B981",
              backgroundColor: "rgba(16, 185, 129, 0.1)",
              borderWidth: 3,
              fill: true,
              tension: 0.4,
              pointBackgroundColor: "#10B981",
              pointBorderColor: "#ffffff",
              pointBorderWidth: 2,
              pointRadius: 6,
              pointHoverRadius: 8,
            },
            {
              label: "Fail Rate (%)",
              data: data.map((point) => point.failRate),
              borderColor: "#EF4444",
              backgroundColor: "rgba(239, 68, 68, 0.1)",
              borderWidth: 3,
              fill: true,
              tension: 0.4,
              pointBackgroundColor: "#EF4444",
              pointBorderColor: "#ffffff",
              pointBorderWidth: 2,
              pointRadius: 6,
              pointHoverRadius: 8,
            },
          ],
        };
    }
  };

  const chartData = getChartData();

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            weight: "bold" as const,
          },
        },
      },
      tooltip: {
        mode: "index" as const,
        intersect: false,
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "#e5e7eb",
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: (context: any) => {
            const dataIndex = context[0].dataIndex;
            const date = new Date(data[dataIndex].date);
            return date.toLocaleDateString("en-US", {
              weekday: "long",
              year: "numeric",
              month: "long",
              day: "numeric",
            });
          },
          afterBody: (context: any) => {
            const dataIndex = context[0].dataIndex;
            const point = data[dataIndex];
            return [
              `Total Tests: ${point.testCount}`,
              `Avg Duration: ${(point.duration / 1000).toFixed(1)}s`,
            ];
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 11,
          },
          color: "#6B7280",
        },
      },
      y: {
        beginAtZero: true,
        max: type === "pass-fail" ? 100 : undefined,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          font: {
            size: 11,
          },
          color: "#6B7280",
          callback: function (value: any) {
            if (type === "duration") {
              return value + "s";
            } else if (type === "retry") {
              return value.toFixed(1);
            } else {
              return value + "%";
            }
          },
        },
      },
    },
    interaction: {
      mode: "nearest" as const,
      axis: "x" as const,
      intersect: false,
    },
    elements: {
      point: {
        hoverBackgroundColor: "#ffffff",
      },
    },
  };

  return (
    <div style={{ height: height }}>
      <Line data={chartData} options={options} />
    </div>
  );
};

export default TrendChart;
