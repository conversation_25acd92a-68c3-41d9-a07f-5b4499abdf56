import React, { useMemo, useState } from "react";
import { Spin, Tooltip } from "antd";
import { TestAnalytics } from "@/types";

interface ErrorWordCloudChartProps {
  data: TestAnalytics[];
  loading?: boolean;
  height?: number;
}

interface WordData {
  word: string;
  count: number;
  fontSize: number;
  color: string;
  weight: string;
}

const ErrorWordCloudChart: React.FC<ErrorWordCloudChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  const [hoveredWord, setHoveredWord] = useState<string | null>(null);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No error message data available
      </div>
    );
  }

  // Enhanced keyword extraction with better filtering
  const extractKeywords = (errorMessages: string[]) => {
    const keywords: Record<string, number> = {};
    
    // Common stop words to filter out
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
      'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
      'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those',
      'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your',
      'his', 'her', 'its', 'our', 'their', 'test', 'tests', 'spec', 'specs', 'file', 'files'
    ]);

    errorMessages.forEach(message => {
      if (!message) return;
      
      // Extract meaningful words (3+ characters, not stop words)
      const words = message
        .toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => 
          word.length >= 3 && 
          !stopWords.has(word) &&
          !/^\d+$/.test(word) // Filter out pure numbers
        );

      words.forEach(word => {
        keywords[word] = (keywords[word] || 0) + 1;
      });
    });

    return keywords;
  };

  // Process error messages from test data
  const errorMessages = useMemo(() => {
    const messages: string[] = [];
    
    data.forEach(test => {
      if (test.status === 'failed' && test.errorMessage) {
        messages.push(test.errorMessage);
      }
    });
    
    return messages;
  }, [data]);

  const wordData = useMemo(() => {
    const keywords = extractKeywords(errorMessages);
    const entries = Object.entries(keywords);
    
    if (entries.length === 0) return [];

    // Sort by frequency and take top 50 words
    const sortedEntries = entries
      .sort(([, a], [, b]) => b - a)
      .slice(0, 50);

    const maxCount = Math.max(...sortedEntries.map(([, count]) => count));
    const minCount = Math.min(...sortedEntries.map(([, count]) => count));

    // Color palette for different frequency ranges
    const colors = [
      '#ef4444', // red-500
      '#f97316', // orange-500
      '#eab308', // yellow-500
      '#22c55e', // green-500
      '#3b82f6', // blue-500
      '#8b5cf6', // violet-500
      '#ec4899', // pink-500
    ];

    return sortedEntries.map(([word, count], index) => {
      const frequency = (count - minCount) / (maxCount - minCount || 1);
      const fontSize = Math.max(12 + frequency * 24, 12); // 12px to 36px
      const colorIndex = Math.floor(frequency * (colors.length - 1));
      
      return {
        word,
        count,
        fontSize,
        color: colors[colorIndex],
        weight: frequency > 0.7 ? 'bold' : frequency > 0.4 ? 'semibold' : 'normal'
      } as WordData;
    });
  }, [errorMessages]);

  if (wordData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No error keywords found
      </div>
    );
  }

  return (
    <div style={{ height: height }} className="overflow-auto">
      <div className="p-4">
        <div className="flex flex-wrap gap-2 justify-center items-center">
          {wordData.map((word, index) => (
            <Tooltip
              key={word.word}
              title={`"${word.word}" appears ${word.count} time${word.count > 1 ? 's' : ''} in error messages`}
            >
              <span
                className={`cursor-pointer transition-all duration-200 hover:scale-110 ${
                  hoveredWord === word.word ? 'opacity-100' : 'opacity-80'
                }`}
                style={{
                  fontSize: `${word.fontSize}px`,
                  color: word.color,
                  fontWeight: word.weight,
                  textShadow: hoveredWord === word.word ? '0 0 8px rgba(0,0,0,0.3)' : 'none'
                }}
                onMouseEnter={() => setHoveredWord(word.word)}
                onMouseLeave={() => setHoveredWord(null)}
              >
                {word.word}
              </span>
            </Tooltip>
          ))}
        </div>
        
        {/* Statistics */}
        <div className="mt-4 pt-4 border-t border-gray-200 text-center">
          <div className="text-sm text-gray-600">
            <span className="font-medium">{wordData.length}</span> unique keywords from{' '}
            <span className="font-medium">{errorMessages.length}</span> error messages
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Hover over words to see frequency • Larger words appear more often
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorWordCloudChart;
