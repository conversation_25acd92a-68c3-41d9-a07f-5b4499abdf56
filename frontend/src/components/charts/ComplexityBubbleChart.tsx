import React from "react";
import { Bubble } from "react-chartjs-2";
import {
  Chart as ChartJS,
  LinearScale,
  PointElement,
  <PERSON><PERSON><PERSON>,
  Legend,
} from "chart.js";
import { Spin } from "antd";
import { TestAnalytics } from "@/types";

// Register Chart.js components
ChartJS.register(LinearScale, PointElement, Tooltip, Legend);

interface ComplexityBubbleChartProps {
  data: TestAnalytics[];
  loading?: boolean;
  height?: number;
}

const ComplexityBubbleChart: React.FC<ComplexityBubbleChartProps> = ({
  data,
  loading = false,
  height = 300,
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No test complexity data available
      </div>
    );
  }

  // Calculate complexity score based on multiple factors
  const calculateComplexity = (test: TestAnalytics) => {
    let complexity = 0;

    // Duration factor (longer tests are more complex)
    complexity += Math.min((test.averageDuration || 0) / 10000, 5); // Max 5 points

    // Retry factor (tests that need retries are more complex)
    complexity += Math.min((test.retryCount || 0) * 2, 3); // Max 3 points

    // File path depth (deeper nested tests might be more complex)
    const pathDepth = (test.filePath || "").split("/").length;
    complexity += Math.min(pathDepth / 3, 2); // Max 2 points

    return Math.max(complexity, 0.5); // Minimum complexity of 0.5
  };

  // Prepare bubble data with error handling
  const bubbleData = data
    .filter((test) => test && typeof test === "object")
    .map((test) => ({
      x: calculateComplexity(test), // Complexity score
      y: Math.max(0, Math.min(100, test.passRate || 0)), // Success rate (0-100)
      r: Math.max((test.totalRuns || 1) / 5, 3), // Bubble size based on total runs
      test: test,
    }))
    .filter(
      (bubble) => !isNaN(bubble.x) && !isNaN(bubble.y) && !isNaN(bubble.r)
    );

  // Check if we have valid bubble data
  if (bubbleData.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No valid test complexity data available
      </div>
    );
  }

  // Create a single dataset with color based on pass rate
  const getColorByPassRate = (passRate: number) => {
    if (passRate >= 90)
      return { bg: "rgba(16, 185, 129, 0.6)", border: "rgb(16, 185, 129)" }; // Green
    if (passRate >= 70)
      return { bg: "rgba(245, 158, 11, 0.6)", border: "rgb(245, 158, 11)" }; // Orange
    return { bg: "rgba(239, 68, 68, 0.6)", border: "rgb(239, 68, 68)" }; // Red
  };

  const datasets = [
    {
      label: "Test Complexity vs Success Rate",
      data: bubbleData,
      backgroundColor: bubbleData.map(
        (bubble) => getColorByPassRate(bubble.y).bg
      ),
      borderColor: bubbleData.map(
        (bubble) => getColorByPassRate(bubble.y).border
      ),
      borderWidth: 2,
    },
  ];

  const chartData = {
    datasets,
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top" as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
            weight: "bold" as const,
          },
        },
      },
      tooltip: {
        backgroundColor: "rgba(0, 0, 0, 0.8)",
        titleColor: "#ffffff",
        bodyColor: "#ffffff",
        borderColor: "#e5e7eb",
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: function (context: any) {
            const test = context[0].raw.test;
            return test.testName;
          },
          label: function (context: any) {
            const bubble = context.raw;
            const test = bubble.test;
            return [
              `Complexity Score: ${bubble.x.toFixed(1)}`,
              `Pass Rate: ${bubble.y.toFixed(1)}%`,
              `Total Runs: ${test.totalRuns}`,
              `Avg Duration: ${(test.averageDuration / 1000).toFixed(1)}s`,
              `Retries: ${test.retryCount}`,
            ];
          },
        },
      },
    },
    scales: {
      x: {
        type: "linear" as const,
        position: "bottom" as const,
        title: {
          display: true,
          text: "Complexity Score",
          font: {
            size: 14,
            weight: "bold" as const,
          },
        },
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          font: {
            size: 11,
          },
          color: "#6B7280",
        },
      },
      y: {
        type: "linear" as const,
        title: {
          display: true,
          text: "Success Rate (%)",
          font: {
            size: 14,
            weight: "bold" as const,
          },
        },
        min: 0,
        max: 100,
        grid: {
          color: "rgba(0, 0, 0, 0.05)",
        },
        ticks: {
          font: {
            size: 11,
          },
          color: "#6B7280",
          callback: function (value: any) {
            return value + "%";
          },
        },
      },
    },
    elements: {
      point: {
        hoverRadius: 8,
      },
    },
  };

  try {
    return (
      <div style={{ height: height }}>
        <Bubble data={chartData} options={options} />
      </div>
    );
  } catch (error) {
    console.error("Error rendering ComplexityBubbleChart:", error);
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        Error rendering complexity chart
      </div>
    );
  }
};

export default ComplexityBubbleChart;
