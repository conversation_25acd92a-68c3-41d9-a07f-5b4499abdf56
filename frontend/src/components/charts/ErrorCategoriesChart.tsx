import React from 'react';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Spin } from 'antd';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ErrorCategoriesChartProps {
  data: Record<string, number>;
  loading?: boolean;
  height?: number;
}

const ErrorCategoriesChart: React.FC<ErrorCategoriesChartProps> = ({ 
  data, 
  loading = false, 
  height = 300 
}) => {
  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Spin size="large" />
      </div>
    );
  }

  const categories = Object.keys(data);
  const values = Object.values(data);

  if (categories.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        No error data available
      </div>
    );
  }

  // Sort by frequency (descending)
  const sortedData = categories
    .map((category, index) => ({ category, value: values[index] }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 10); // Show top 10 error categories

  const sortedCategories = sortedData.map(item => item.category);
  const sortedValues = sortedData.map(item => item.value);

  // Generate gradient colors from red to orange
  const colors = sortedValues.map((_, index) => {
    const intensity = 1 - (index / sortedValues.length) * 0.5; // From 1 to 0.5
    const red = Math.floor(239 * intensity); // EF = 239
    const green = Math.floor(68 + (158 * (1 - intensity))); // 44 to F5 (68 to 245)
    const blue = 68; // Keep blue constant
    return `rgb(${red}, ${green}, ${blue})`;
  });

  const chartData = {
    labels: sortedCategories.map(category => {
      // Truncate long category names
      return category.length > 20 ? category.substring(0, 20) + '...' : category;
    }),
    datasets: [
      {
        label: 'Error Count',
        data: sortedValues,
        backgroundColor: colors,
        borderColor: colors.map(color => color.replace('rgb', 'rgba').replace(')', ', 0.8)')),
        borderWidth: 2,
        borderRadius: 6,
        borderSkipped: false,
        hoverBackgroundColor: colors.map(color => color.replace('rgb', 'rgba').replace(')', ', 0.7)')),
        hoverBorderWidth: 3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    indexAxis: 'y' as const, // Horizontal bar chart
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        cornerRadius: 8,
        padding: 12,
        callbacks: {
          title: function(context: any) {
            const fullCategory = sortedCategories[context[0].dataIndex];
            return `Error: ${fullCategory}`;
          },
          label: function(context: any) {
            const value = context.parsed.x;
            const total = sortedValues.reduce((sum, val) => sum + val, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `Count: ${value} (${percentage}%)`;
          },
        },
      },
    },
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
        },
        ticks: {
          font: {
            size: 11,
          },
          color: '#6B7280',
          precision: 0,
        },
      },
      y: {
        grid: {
          display: false,
        },
        ticks: {
          font: {
            size: 10,
          },
          color: '#6B7280',
          maxRotation: 0,
          minRotation: 0,
        },
      },
    },
    elements: {
      bar: {
        borderWidth: 2,
      },
    },
  };

  return (
    <div style={{ height: height }}>
      <Bar data={chartData} options={options} />
    </div>
  );
};

export default ErrorCategoriesChart;
