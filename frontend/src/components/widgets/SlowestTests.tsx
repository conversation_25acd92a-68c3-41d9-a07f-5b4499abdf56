import React from "react";
import { Card, List, Progress, Tag, Spin } from "antd";
import { ClockCircleOutlined, FileTextOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { TestAnalytics } from "@/types";

interface SlowestTestsProps {
  tests: TestAnalytics[];
  loading?: boolean;
  limit?: number;
}

const SlowestTests: React.FC<SlowestTestsProps> = ({
  tests,
  loading = false,
  limit = 5,
}) => {
  if (loading) {
    return (
      <Card title="Slowest Tests" className="h-80">
        <div className="flex items-center justify-center h-full">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  const displayTests = tests.slice(0, limit);

  if (displayTests.length === 0) {
    return (
      <Card title="Slowest Tests" className="h-80">
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <ClockCircleOutlined className="text-4xl text-blue-500 mb-4" />
            <p>No test duration data available</p>
          </div>
        </div>
      </Card>
    );
  }

  // Find the maximum duration for progress bar scaling
  const maxDuration = Math.max(
    ...displayTests.map((test) => test.averageDuration)
  );

  const formatDuration = (duration: number): string => {
    const seconds = duration / 1000;
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
    }
  };

  const getDurationColor = (duration: number): string => {
    const seconds = duration / 1000;
    if (seconds > 60) return "#EF4444"; // Red for > 1 minute
    if (seconds > 30) return "#F59E0B"; // Orange for > 30 seconds
    return "#10B981"; // Green for <= 30 seconds
  };

  return (
    <Card
      title={
        <div className="flex items-center space-x-2">
          <ClockCircleOutlined className="text-blue-500" />
          <span>Slowest Tests</span>
        </div>
      }
      className="h-80"
    >
      <List
        dataSource={displayTests}
        renderItem={(test, index) => (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <List.Item className="border-none px-0">
              <div className="w-full">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {test.testName}
                    </h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <FileTextOutlined className="text-xs text-gray-400" />
                      <span className="text-xs text-gray-500 truncate">
                        {test.filePath}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Tag
                      color={
                        getDurationColor(test.averageDuration) === "#EF4444"
                          ? "red"
                          : getDurationColor(test.averageDuration) === "#F59E0B"
                          ? "orange"
                          : "green"
                      }
                      className="text-xs"
                    >
                      {formatDuration(test.averageDuration)}
                    </Tag>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>Average Duration</span>
                    <span>{formatDuration(test.averageDuration)}</span>
                  </div>
                  <Progress
                    percent={(test.averageDuration / maxDuration) * 100}
                    strokeColor={getDurationColor(test.averageDuration)}
                    trailColor="#F3F4F6"
                    size="small"
                    showInfo={false}
                  />

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Total Runs: {test.totalRuns}</span>
                    <span>Pass Rate: {test.passRate.toFixed(1)}%</span>
                  </div>

                  {test.tags.length > 0 && (
                    <div className="flex items-center space-x-1">
                      {test.tags.map((tag) => (
                        <Tag key={tag} size="small" className="text-xs">
                          {tag}
                        </Tag>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </List.Item>
          </motion.div>
        )}
      />
    </Card>
  );
};

export default SlowestTests;
