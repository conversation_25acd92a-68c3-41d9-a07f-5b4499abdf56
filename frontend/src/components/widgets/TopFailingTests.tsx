import React from "react";
import { Card, List, Progress, Tag, Spin } from "antd";
import { CloseCircleOutlined, FileTextOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { TestAnalytics } from "@/types";

interface TopFailingTestsProps {
  tests: TestAnalytics[];
  loading?: boolean;
  limit?: number;
}

const TopFailingTests: React.FC<TopFailingTestsProps> = ({
  tests,
  loading = false,
  limit = 5,
}) => {
  if (loading) {
    return (
      <Card title="Top Failing Tests" className="h-80">
        <div className="flex items-center justify-center h-full">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  const displayTests = tests.slice(0, limit);

  if (displayTests.length === 0) {
    return (
      <Card title="Top Failing Tests" className="h-80">
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <CloseCircleOutlined className="text-4xl text-green-500 mb-4" />
            <p>No failing tests found!</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <div className="flex items-center space-x-2">
          <CloseCircleOutlined className="text-red-500" />
          <span>Top Failing Tests</span>
        </div>
      }
      className="h-80"
    >
      <List
        dataSource={displayTests}
        renderItem={(test, index) => (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <List.Item className="border-none px-0">
              <div className="w-full">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {test.testName}
                    </h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <FileTextOutlined className="text-xs text-gray-400" />
                      <span className="text-xs text-gray-500 truncate">
                        {test.filePath}
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Tag color="red" className="text-xs">
                      {test.failRate.toFixed(1)}% fail
                    </Tag>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>Fail Rate</span>
                    <span>{test.failRate.toFixed(1)}%</span>
                  </div>
                  <Progress
                    percent={test.failRate}
                    strokeColor="#EF4444"
                    trailColor="#F3F4F6"
                    size="small"
                    showInfo={false}
                  />

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Total Runs: {test.totalRuns}</span>
                    <span>Retries: {test.retryCount}</span>
                  </div>

                  {test.tags.length > 0 && (
                    <div className="flex items-center space-x-1">
                      {test.tags.map((tag) => (
                        <Tag key={tag} size="small" className="text-xs">
                          {tag}
                        </Tag>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </List.Item>
          </motion.div>
        )}
      />
    </Card>
  );
};

export default TopFailingTests;
