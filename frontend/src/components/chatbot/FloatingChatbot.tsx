import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, Drawer, Input, Avatar, Typography, Tag, Tooltip } from "antd";
import {
  MessageOutlined,
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  CloseOutlined,
  BulbOutlined,
} from "@ant-design/icons";
import { motion, AnimatePresence } from "framer-motion";
// import ReactMarkdown from 'react-markdown';
import apiService from "@/services/api";
import "./FloatingChatbot.css";

const { Text } = Typography;
const { TextArea } = Input;

interface ChatMessage {
  id: string;
  message: string;
  isUser: boolean;
  timestamp: Date;
  data?: any;
  suggestions?: string[];
}

interface ChatbotStatus {
  available: boolean;
  message: string;
  dataPoints: number;
  testFiles: number;
  lastUpdated: string;
}

const FloatingChatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<ChatbotStatus | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  // Load initial data when chatbot opens
  useEffect(() => {
    if (isOpen && !status) {
      loadChatbotStatus();
      loadSuggestions();

      // Add welcome message
      if (messages.length === 0) {
        addWelcomeMessage();
      }
    }
  }, [isOpen]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    // Use setTimeout to ensure DOM has been updated
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({
        behavior: "smooth",
        block: "end",
        inline: "nearest",
      });
    }, 100);
  };

  const loadChatbotStatus = async () => {
    try {
      const data = await apiService.getChatStatus();
      setStatus(data);
    } catch (error) {
      console.error("Failed to load chatbot status:", error);
    }
  };

  const loadSuggestions = async () => {
    try {
      const data = await apiService.getChatSuggestions();
      setSuggestions(data.suggestions || []);
    } catch (error) {
      console.error("Failed to load suggestions:", error);
    }
  };

  const addWelcomeMessage = () => {
    const welcomeMessage: ChatMessage = {
      id: "welcome",
      message: `👋 Hi! I'm your Test Analytics Assistant

I can help you analyze your test data and answer questions about:
→ Test performance and trends
→ Failure analysis and error patterns
→ Project comparisons
→ Specific test case details

What would you like to know about your tests?`,
      isUser: false,
      timestamp: new Date(),
      suggestions: suggestions.slice(0, 4),
    };
    setMessages([welcomeMessage]);
  };

  // Helper function to detect casual greetings
  const isCasualGreeting = (message: string): boolean => {
    const trimmed = message.toLowerCase().trim();
    const greetings = ["hi", "hey", "hello", "hiya", "sup", "yo"];
    const simpleMessages = [
      "thanks",
      "thank you",
      "ok",
      "okay",
      "cool",
      "nice",
      "great",
      "awesome",
      "perfect",
    ];

    // Check exact matches for short greetings
    for (const greeting of greetings) {
      if (
        trimmed === greeting ||
        trimmed === greeting + "!" ||
        trimmed === greeting + "."
      ) {
        return true;
      }
    }

    // Check exact matches for simple responses
    for (const simple of simpleMessages) {
      if (
        trimmed === simple ||
        trimmed === simple + "!" ||
        trimmed === simple + "."
      ) {
        return true;
      }
    }

    // Check if it's a very short message (likely casual)
    const words = trimmed.split(/\s+/);
    if (words.length <= 2 && trimmed.length <= 10) {
      for (const word of words) {
        for (const greeting of greetings) {
          if (word.includes(greeting)) {
            return true;
          }
        }
      }
    }

    return false;
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    // Add user message
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      message: message.trim(),
      isUser: true,
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputValue("");
    setIsLoading(true);

    try {
      // Check for casual greetings first
      if (isCasualGreeting(message.trim())) {
        // Handle casual greeting locally
        const greetingResponse: ChatMessage = {
          id: (Date.now() + 1).toString(),
          message: `👋 **Hello! I'm here to help you with your test data analysis.**

I notice you sent a casual message. To provide meaningful insights, I need you to ask specific questions about your test data.

**Here are some examples of what you can ask:**
• "Show me an overview of my test results"
• "Which tests are failing the most?"
• "What are the slowest tests?"
• "How is my test performance trending?"
• "Compare performance across projects"
• "Show me flaky tests"

**What would you like to know about your tests?**`,
          isUser: false,
          timestamp: new Date(),
          suggestions: [
            "Show test overview",
            "Which tests are failing?",
            "What are the slowest tests?",
            "Show test trends",
            "Compare projects",
            "Analyze flaky tests",
          ],
        };

        setMessages((prev) => [...prev, greetingResponse]);
        setIsLoading(false);
        return;
      }

      // Send to chatbot API for meaningful queries
      console.log("Sending chat message:", message.trim());
      const data = await apiService.sendChatMessage(message.trim(), {}, {});
      console.log("Chat response:", data);

      if (data.success) {
        // Add bot response
        const botMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          message: data.message,
          isUser: false,
          timestamp: new Date(data.timestamp),
          data: data.data,
          suggestions: data.suggestions,
        };

        setMessages((prev) => [...prev, botMessage]);
      } else {
        throw new Error("Failed to get response");
      }
    } catch (error) {
      console.error("Chat error:", error);

      // Add error message
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        message:
          "❌ Sorry, I encountered an error processing your request. Please try again.",
        isUser: false,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    sendMessage(suggestion);
    // Ensure scroll after suggestion click
    setTimeout(() => scrollToBottom(), 200);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessage(inputValue);
    }
  };

  return (
    <>
      {/* Floating Chat Button - Only show when chatbot is closed */}
      <AnimatePresence mode="wait">
        {!isOpen && (
          <motion.div
            key="chat-button"
            className="floating-chat-button"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ type: "spring", stiffness: 260, damping: 20 }}
            style={{
              display: isOpen ? "none" : "block",
              visibility: isOpen ? "hidden" : "visible",
              zIndex: isOpen ? -1 : 1001,
            }}
          >
            <Tooltip
              title="Ask questions about your test data"
              placement="left"
            >
              <Button
                type="primary"
                shape="circle"
                size="large"
                icon={<MessageOutlined />}
                onClick={() => setIsOpen(true)}
                style={{
                  width: 60,
                  height: 60,
                  fontSize: 20,
                  background:
                    "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  border: "none",
                }}
              />
            </Tooltip>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Drawer */}
      <Drawer
        title="Test Analytics Assistant"
        placement="right"
        width={400}
        open={isOpen}
        onClose={() => setIsOpen(false)}
        closeIcon={<CloseOutlined />}
        className="chatbot-drawer"
        style={{ zIndex: 1003 }}
        styles={{
          body: {
            padding: 0,
            height: "100%",
            display: "flex",
            flexDirection: "column",
          },
          header: {
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            color: "white",
            borderBottom: "1px solid rgba(255,255,255,0.1)",
          },
        }}
      >
        {/* Messages Container */}
        <div className="messages-container">
          <AnimatePresence>
            {messages.map((msg) => (
              <motion.div
                key={msg.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3 }}
                className={`message-wrapper ${msg.isUser ? "user" : "bot"}`}
              >
                <div className="message-content">
                  <Avatar
                    size="small"
                    icon={msg.isUser ? <UserOutlined /> : <RobotOutlined />}
                    className={`message-avatar ${
                      msg.isUser ? "bg-blue-600" : "bg-gray-500"
                    }`}
                  />
                  <div className="flex-1">
                    <div
                      className={`message-bubble ${
                        msg.isUser ? "user" : "bot"
                      }`}
                    >
                      <div className="message-text">{msg.message}</div>
                    </div>
                    <div className="message-timestamp">
                      {msg.timestamp.toLocaleTimeString()}
                    </div>

                    {/* Suggestions */}
                    {msg.suggestions && msg.suggestions.length > 0 && (
                      <div className="suggestions-container">
                        <div className="flex items-center gap-1 mb-2">
                          <BulbOutlined className="text-yellow-500" />
                          <Text className="text-xs text-gray-600 font-medium">
                            Try asking:
                          </Text>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {msg.suggestions.map((suggestion, index) => (
                            <Tag
                              key={index}
                              className="suggestion-tag"
                              onClick={() => handleSuggestionClick(suggestion)}
                            >
                              {suggestion}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Loading indicator */}
          {isLoading && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="message-wrapper bot"
            >
              <div className="message-content">
                <Avatar
                  size="small"
                  icon={<RobotOutlined />}
                  className="message-avatar bg-gray-500"
                />
                <div className="typing-indicator">
                  <div className="typing-dots">
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                  </div>
                  <Text className="text-gray-600 text-sm">
                    Analyzing your data...
                  </Text>
                </div>
              </div>
            </motion.div>
          )}

          <div ref={messagesEndRef} className="messages-end-ref" />
        </div>

        {/* Input Area */}
        <div className="chat-input-area">
          <div className="chat-input-container">
            <TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask me about your test data... (e.g., 'Show me failing tests' or 'What are the slowest tests?')"
              autoSize={{ minRows: 1, maxRows: 4 }}
              disabled={isLoading}
              className="chat-textarea"
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={() => sendMessage(inputValue)}
              loading={isLoading}
              disabled={!inputValue.trim()}
              className="chat-send-button"
            />
          </div>
        </div>
      </Drawer>
    </>
  );
};

export default FloatingChatbot;
