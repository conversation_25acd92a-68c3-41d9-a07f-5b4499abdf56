/* Chatbot Drawer Styles */
.chatbot-drawer .ant-drawer-body {
  padding: 0 !important;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatbot-drawer .ant-drawer-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
}

.chatbot-drawer .ant-drawer-header .ant-drawer-title {
  color: white !important;
  font-weight: 600;
}

/* Message Bubbles */
.message-bubble {
  border-radius: 16px;
  padding: 10px 14px;
  max-width: 280px;
  word-wrap: break-word;
  word-break: break-word;
  position: relative;
  display: inline-block;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-bubble.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message-bubble.bot {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #333;
}

/* Input Area Styling */
.chat-input-area {
  background: white;
  border-top: 1px solid #e9ecef;
  padding: 16px;
  flex-shrink: 0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.chat-input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  max-width: 100%;
}

.chat-textarea {
  flex: 1;
  border-radius: 20px !important;
  border: 1px solid #d1d5db !important;
  padding: 8px 16px !important;
  resize: none !important;
  font-size: 14px;
  line-height: 1.4;
  min-height: 40px;
  max-height: 120px;
}

.chat-textarea:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1) !important;
}

.chat-send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.chat-send-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.chat-send-button:disabled {
  background: #d1d5db !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  padding-bottom: 24px; /* Extra padding at bottom for better scroll */
  background: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-height: 0;
  scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Message Layout */
.message-wrapper {
  display: flex;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 0;
}

.message-wrapper.user {
  justify-content: flex-end;
}

.message-wrapper.bot {
  justify-content: flex-start;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 85%;
  flex-direction: row;
}

.message-wrapper.user .message-content {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 1px;
}

.message-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
  white-space: pre-line;
}

.message-timestamp {
  font-size: 11px;
  color: #8e8e93;
  margin-top: 2px;
  text-align: right;
}

.message-wrapper.bot .message-timestamp {
  text-align: left;
}

/* Scroll End Element */
.messages-end-ref {
  height: 20px;
  flex-shrink: 0;
  width: 100%;
}

/* Suggestions */
.suggestions-container {
  margin-top: 8px;
  padding-left: 0;
}

.suggestion-tag {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 10px;
  font-size: 11px;
  padding: 2px 6px;
}

.suggestion-tag:hover {
  background: #e6f7ff !important;
  border-color: #1890ff !important;
  color: #1890ff !important;
  transform: translateY(-1px);
}

/* Loading Animation */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 18px;
  border: 1px solid #e9ecef;
  margin-right: auto;
  max-width: 200px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #8e8e93;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%,
  80%,
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Floating Button Animation */
.floating-chat-button {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1001; /* Lower than drawer z-index */
  transition: all 0.3s ease;
}

.floating-chat-button:hover {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chatbot-drawer .ant-drawer-content {
    width: 100vw !important;
  }

  .message-content {
    max-width: 90%;
  }

  .chat-input-area {
    padding: 12px;
  }
}
