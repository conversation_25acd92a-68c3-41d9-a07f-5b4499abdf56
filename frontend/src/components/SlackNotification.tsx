import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Select,
  DatePicker,
  Space,
  Typography,
  Alert,
  Spin,
  message,
} from "antd";
import {
  SlackOutlined,
  CalendarOutlined,
  SendOutlined,
  ExperimentOutlined,
} from "@ant-design/icons";
import dayjs, { Dayjs } from "dayjs";
import apiService from "@/services/api";
import { SlackNotificationRequest } from "@/types";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface SlackNotificationProps {
  trigger?: React.ReactNode;
}

const SlackNotification: React.FC<SlackNotificationProps> = ({ trigger }) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [timeRange, setTimeRange] = useState<string>("24h");
  const [customDateRange, setCustomDateRange] = useState<[Dayjs, Dayjs] | null>(
    null
  );

  const timeRangeOptions = [
    { label: "Last 24 Hours", value: "24h" },
    { label: "Last 7 Days", value: "7d" },
    { label: "Last 30 Days", value: "30d" },
    { label: "All Time", value: "all" },
    { label: "Custom Range", value: "custom" },
  ];

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setTimeRange("24h");
    setCustomDateRange(null);
  };

  const handleSendNotification = async () => {
    try {
      setLoading(true);

      // Validate custom date range if selected
      if (timeRange === "custom") {
        if (!customDateRange || !customDateRange[0] || !customDateRange[1]) {
          message.error("Please select a valid date range");
          return;
        }
      }

      // Prepare request
      const request: SlackNotificationRequest = {
        timeRange,
      };

      if (timeRange === "custom" && customDateRange) {
        request.startDate = customDateRange[0].format("YYYY-MM-DD");
        request.endDate = customDateRange[1].format("YYYY-MM-DD");
      }

      // Send notification
      const response = await apiService.sendSlackNotification(request);

      message.success(
        `Slack notification sent successfully! 
        📊 ${response.metrics.totalTests} tests analyzed 
        ✅ ${response.metrics.passRate.toFixed(1)}% pass rate`
      );

      setIsModalVisible(false);
    } catch (error) {
      console.error("Failed to send Slack notification:", error);
      message.error("Failed to send Slack notification. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setTestLoading(true);
      await apiService.testSlackConnection();
      message.success("Test message sent to Slack successfully!");
    } catch (error) {
      console.error("Failed to test Slack connection:", error);
      message.error(
        "Failed to send test message. Please check your Slack configuration."
      );
    } finally {
      setTestLoading(false);
    }
  };

  const getTimeRangeDescription = () => {
    switch (timeRange) {
      case "24h":
        return "Send test results from the last 24 hours";
      case "7d":
        return "Send test results from the last 7 days";
      case "30d":
        return "Send test results from the last 30 days";
      case "all":
        return "Send all available test results";
      case "custom":
        return "Send test results from your selected date range";
      default:
        return "";
    }
  };

  const defaultTrigger = (
    <Button
      type="primary"
      icon={<SlackOutlined />}
      onClick={showModal}
      className="bg-gradient-to-r from-purple-500 to-blue-500 border-0 hover:from-purple-600 hover:to-blue-600"
    >
      Send to Slack
    </Button>
  );

  return (
    <>
      {trigger ? <div onClick={showModal}>{trigger}</div> : defaultTrigger}

      <Modal
        title={
          <div className="flex items-center space-x-2">
            <SlackOutlined className="text-purple-500" />
            <Title level={4} className="mb-0">
              Send Test Report to Slack
            </Title>
          </div>
        }
        open={isModalVisible}
        onCancel={handleCancel}
        footer={null}
        width={600}
        className="slack-notification-modal"
      >
        <div className="space-y-6">
          {/* Channel Info */}
          <Alert
            message="Notification Channel"
            description={
              <div>
                <Text strong>Channel:</Text> #ztb-e2e-report-alerts
                (C091V2CF5DF)
                <br />
                <Text type="secondary">
                  Test reports will be sent to this Slack channel with detailed
                  metrics and insights.
                </Text>
              </div>
            }
            type="info"
            showIcon
            icon={<SlackOutlined />}
          />

          {/* Time Range Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <CalendarOutlined className="mr-2" />
              Select Time Range
            </label>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              options={timeRangeOptions}
              className="w-full"
              size="large"
            />
            <Text type="secondary" className="text-xs mt-1 block">
              {getTimeRangeDescription()}
            </Text>
          </div>

          {/* Custom Date Range */}
          {timeRange === "custom" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Custom Date Range
              </label>
              <RangePicker
                value={customDateRange}
                onChange={setCustomDateRange}
                className="w-full"
                size="large"
                disabledDate={(current) =>
                  current && current > dayjs().endOf("day")
                }
                placeholder={["Start Date", "End Date"]}
              />
            </div>
          )}

          {/* Preview Message */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <Text strong className="block mb-2">
              📋 Message Preview:
            </Text>
            <div className="bg-white p-3 rounded border-l-4 border-purple-500">
              <Text className="block">
                🚀 <strong>ZTB-E2E Test Report</strong>
              </Text>
              <Text type="secondary" className="block text-sm">
                Time Range:{" "}
                {timeRangeOptions.find((opt) => opt.value === timeRange)?.label}
              </Text>
              <Text type="secondary" className="block text-sm mt-1">
                📊 Comprehensive test metrics with pass/fail rates, insights,
                and recommendations
              </Text>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between items-center pt-4 border-t">
            <Button
              icon={<ExperimentOutlined />}
              onClick={handleTestConnection}
              loading={testLoading}
              className="text-gray-600"
            >
              Test Connection
            </Button>

            <Space>
              <Button onClick={handleCancel}>Cancel</Button>
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendNotification}
                loading={loading}
                className="bg-gradient-to-r from-purple-500 to-blue-500 border-0 hover:from-purple-600 hover:to-blue-600"
              >
                Send Report
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default SlackNotification;
