import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Al<PERSON>, Popover } from "antd";
import {
  EyeOutlined,
  PlayCircleOutlined,
  DownloadOutlined,
  FileTextOutlined,
  FileZipOutlined,
  PictureOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import apiService from "@/services/api";
import { AttachmentInfo } from "@/types";

interface AttachmentsViewerProps {
  attachments?: AttachmentInfo[];
  testName: string;
  inline?: boolean; // New prop to control rendering mode
}

const AttachmentsViewer: React.FC<AttachmentsViewerProps> = ({
  attachments,
  testName,
  inline = false,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loadingAttachments, setLoadingAttachments] = useState<{
    [key: string]: boolean;
  }>({});
  const [attachmentUrls, setAttachmentUrls] = useState<{
    [key: string]: string;
  }>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const getAttachmentIcon = (type: string, contentType: string) => {
    switch (type) {
      case "video":
        return <PlayCircleOutlined className="text-blue-500" />;
      case "screenshot":
        return <PictureOutlined className="text-green-500" />;
      case "trace":
        return <FileZipOutlined className="text-purple-500" />;
      case "log":
        return <FileTextOutlined className="text-orange-500" />;
      default:
        if (contentType.includes("image")) {
          return <PictureOutlined className="text-green-500" />;
        } else if (contentType.includes("video")) {
          return <PlayCircleOutlined className="text-blue-500" />;
        } else if (contentType.includes("zip")) {
          return <FileZipOutlined className="text-purple-500" />;
        } else {
          return <FileTextOutlined className="text-gray-500" />;
        }
    }
  };

  const getAttachmentUrl = async (attachment: AttachmentInfo) => {
    const key = attachment.s3Key;

    if (attachmentUrls[key]) {
      return attachmentUrls[key];
    }

    setLoadingAttachments((prev) => ({ ...prev, [key]: true }));
    setErrors((prev) => ({ ...prev, [key]: "" }));

    try {
      let response;
      if (
        attachment.type === "video" ||
        attachment.contentType.includes("video")
      ) {
        response = await apiService.getVideoURL(attachment.s3Key);
        const url = response.videoUrl;
        setAttachmentUrls((prev) => ({ ...prev, [key]: url }));
        return url;
      } else if (
        attachment.type === "screenshot" ||
        attachment.contentType.includes("image")
      ) {
        response = await apiService.getScreenshotURL(attachment.s3Key);
        const url = response.screenshotUrl;
        setAttachmentUrls((prev) => ({ ...prev, [key]: url }));
        return url;
      } else {
        // For other file types (logs, traces, etc.), use the generic attachment endpoint
        response = await apiService.getAttachmentURL(attachment.s3Key);
        const url = response.attachmentUrl;
        setAttachmentUrls((prev) => ({ ...prev, [key]: url }));
        return url;
      }
    } catch (error) {
      console.error(`Failed to get URL for ${attachment.fileName}:`, error);
      setErrors((prev) => ({ ...prev, [key]: "Failed to load attachment" }));
      return null;
    } finally {
      setLoadingAttachments((prev) => ({ ...prev, [key]: false }));
    }
  };

  const handleViewAttachment = async (attachment: AttachmentInfo) => {
    const url = await getAttachmentUrl(attachment);
    if (url) {
      if (
        attachment.type === "video" ||
        attachment.contentType.includes("video")
      ) {
        // For videos, we'll handle them in the modal
        return;
      } else {
        // For other files, open in new tab
        window.open(url, "_blank");
      }
    }
  };

  const handleDownloadAttachment = async (attachment: AttachmentInfo) => {
    const url = await getAttachmentUrl(attachment);
    if (url) {
      const link = document.createElement("a");
      link.href = url;
      link.download = attachment.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Render inline video player in a popover
  const renderInlineVideoPlayer = (attachment: AttachmentInfo) => {
    const key = attachment.s3Key;
    const isLoading = loadingAttachments[key];
    const error = errors[key];
    const presignedUrl = attachmentUrls[key];

    if (error) {
      return <Alert message={error} type="error" size="small" />;
    }

    if (isLoading) {
      return (
        <div className="flex items-center justify-center p-4">
          <Spin size="small" />
          <span className="ml-2">Loading video...</span>
        </div>
      );
    }

    if (!presignedUrl) {
      return (
        <div className="p-4 text-center">
          <Button
            size="small"
            onClick={() => getAttachmentUrl(attachment)}
            loading={isLoading}
          >
            Load Video
          </Button>
        </div>
      );
    }

    return (
      <div style={{ width: 400, maxHeight: 300 }}>
        <video
          controls
          style={{ width: "100%", maxHeight: "250px" }}
          preload="metadata"
        >
          <source src={presignedUrl} type="video/webm" />
          <source src={presignedUrl} type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        <div className="mt-2 text-xs text-gray-500 text-center">
          {attachment.fileName}
        </div>
      </div>
    );
  };

  const renderAttachmentActions = (attachment: AttachmentInfo) => {
    const key = attachment.s3Key;
    const isLoading = loadingAttachments[key];
    const error = errors[key];
    const isVideo =
      attachment.type === "video" || attachment.contentType.includes("video");

    return (
      <div className="flex gap-2">
        {isVideo ? (
          <Popover
            content={renderInlineVideoPlayer(attachment)}
            title={`Video: ${attachment.fileName}`}
            trigger="click"
            placement="left"
            overlayStyle={{ maxWidth: 450 }}
          >
            <Tooltip title="Play Video">
              <Button
                type="text"
                size="small"
                icon={isLoading ? <LoadingOutlined /> : <PlayCircleOutlined />}
                disabled={isLoading || !!error}
              />
            </Tooltip>
          </Popover>
        ) : (
          <Tooltip title="View">
            <Button
              type="text"
              size="small"
              icon={isLoading ? <LoadingOutlined /> : <EyeOutlined />}
              onClick={() => handleViewAttachment(attachment)}
              disabled={isLoading || !!error}
            />
          </Tooltip>
        )}
        <Tooltip title="Download">
          <Button
            type="text"
            size="small"
            icon={isLoading ? <LoadingOutlined /> : <DownloadOutlined />}
            onClick={() => handleDownloadAttachment(attachment)}
            disabled={isLoading || !!error}
          />
        </Tooltip>
      </div>
    );
  };

  if (!attachments || attachments.length === 0) {
    if (inline) {
      return (
        <div className="text-center py-8">
          <FileTextOutlined className="text-4xl text-gray-300 mb-2" />
          <p className="text-gray-500">No attachments available</p>
        </div>
      );
    }
    return (
      <Tooltip title="No attachments available">
        <Button type="text" icon={<EyeOutlined />} disabled />
      </Tooltip>
    );
  }

  // If inline mode, render the content directly without button/modal
  if (inline) {
    // Group attachments by attempt (original vs retries)
    const groupedAttachments = attachments.reduce(
      (groups: { [key: string]: AttachmentInfo[] }, attachment) => {
        // Use attemptIndex if available, otherwise fall back to name parsing
        let attemptKey: string;
        if ((attachment as any).attemptIndex !== undefined) {
          const attemptIndex = (attachment as any).attemptIndex;
          attemptKey =
            attemptIndex === 0 ? "Original Attempt" : `(Retry ${attemptIndex})`;
        } else {
          // Fallback to name-based detection
          const isRetry = attachment.name.includes("(Retry ");
          attemptKey = isRetry
            ? attachment.name.match(/\(Retry (\d+)\)/)?.[0] || "Unknown Retry"
            : "Original Attempt";
        }

        if (!groups[attemptKey]) {
          groups[attemptKey] = [];
        }
        groups[attemptKey].push(attachment);
        return groups;
      },
      {}
    );

    const attemptKeys = Object.keys(groupedAttachments).sort((a, b) => {
      if (a === "Original Attempt") return -1;
      if (b === "Original Attempt") return 1;
      return a.localeCompare(b);
    });

    return (
      <div>
        {attemptKeys.length > 1 ? (
          // Show grouped by attempts if there are retries
          <div className="space-y-4">
            {attemptKeys.map((attemptKey) => (
              <div key={attemptKey}>
                <div className="flex items-center space-x-2 mb-3 pb-2 border-b border-gray-200">
                  <span
                    className={`w-3 h-3 rounded-full ${
                      attemptKey === "Original Attempt"
                        ? "bg-blue-500"
                        : "bg-orange-500"
                    }`}
                  />
                  <span className="font-medium text-gray-700">
                    {attemptKey}
                  </span>
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {groupedAttachments[attemptKey].length} files
                  </span>
                </div>
                <List
                  dataSource={groupedAttachments[attemptKey]}
                  renderItem={(attachment) => (
                    <List.Item
                      key={attachment.s3Key}
                      actions={[renderAttachmentActions(attachment)]}
                    >
                      <List.Item.Meta
                        avatar={getAttachmentIcon(
                          attachment.type,
                          attachment.contentType
                        )}
                        title={
                          <div className="flex items-center gap-2">
                            <span>{attachment.fileName}</span>
                            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                              {attachment.type}
                            </span>
                          </div>
                        }
                        description={
                          <div>
                            <div className="text-sm text-gray-600">
                              {attachment.contentType}
                            </div>
                            {errors[attachment.s3Key] && (
                              <Alert
                                message={errors[attachment.s3Key]}
                                type="error"
                                size="small"
                                className="mt-1"
                              />
                            )}
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </div>
            ))}
          </div>
        ) : (
          // Show simple list if no retries
          <List
            dataSource={attachments}
            renderItem={(attachment) => (
              <List.Item
                key={attachment.s3Key}
                actions={[renderAttachmentActions(attachment)]}
              >
                <List.Item.Meta
                  avatar={getAttachmentIcon(
                    attachment.type,
                    attachment.contentType
                  )}
                  title={
                    <div className="flex items-center gap-2">
                      <span>{attachment.fileName}</span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {attachment.type}
                      </span>
                    </div>
                  }
                  description={
                    <div>
                      <div className="text-sm text-gray-600">
                        {attachment.contentType}
                      </div>
                      {errors[attachment.s3Key] && (
                        <Alert
                          message={errors[attachment.s3Key]}
                          type="error"
                          size="small"
                          className="mt-1"
                        />
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </div>
    );
  }

  return (
    <div>
      <Tooltip title="View Attachments">
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => setModalVisible(true)}
        />
      </Tooltip>

      <Modal
        title={`Attachments: ${testName}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            Close
          </Button>,
        ]}
        width={800}
      >
        <List
          dataSource={attachments}
          renderItem={(attachment) => (
            <List.Item
              key={attachment.s3Key}
              actions={[renderAttachmentActions(attachment)]}
            >
              <List.Item.Meta
                avatar={getAttachmentIcon(
                  attachment.type,
                  attachment.contentType
                )}
                title={
                  <div className="flex items-center gap-2">
                    <span>{attachment.fileName}</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      {attachment.type}
                    </span>
                  </div>
                }
                description={
                  <div>
                    <div className="text-sm text-gray-600">
                      {attachment.contentType}
                    </div>
                    {errors[attachment.s3Key] && (
                      <Alert
                        message={errors[attachment.s3Key]}
                        type="error"
                        size="small"
                        className="mt-1"
                      />
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Modal>
    </div>
  );
};

export default AttachmentsViewer;
