import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Toolt<PERSON>, Image } from "antd";
import { PictureOutlined, LoadingOutlined } from "@ant-design/icons";
import apiService from "@/services/api";

interface ScreenshotGalleryProps {
  screenshotUrls?: string[];
  screenshotS3Keys?: string[];
  testName: string;
  fileName?: string; // Add fileName prop
}

const ScreenshotGallery: React.FC<ScreenshotGalleryProps> = ({
  screenshotUrls = [],
  screenshotS3Keys = [],
  testName,
  fileName,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [presignedUrls, setPresignedUrls] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  const hasScreenshots = screenshotUrls.length > 0 || screenshotS3Keys.length > 0;

  const handleOpenGallery = async () => {
    if (!hasScreenshots) {
      return;
    }

    setLoading(true);
    setModalVisible(true);
    setError(null);

    try {
      // If we have direct URLs, use them
      if (screenshotUrls.length > 0) {
        setPresignedUrls(screenshotUrls);
      } 
      // If we have S3 keys, get presigned URLs
      else if (screenshotS3Keys.length > 0) {
        const urls = await Promise.all(
          screenshotS3Keys.map(async (key) => {
            // Modify the key based on fileName if needed
            const modifiedKey = constructScreenshotKey(key, testName, fileName);
            const response = await apiService.getPresignedUrl(modifiedKey);
            return response.url;
          })
        );
        setPresignedUrls(urls);
      }
    } catch (err) {
      console.error("Failed to get screenshot URLs:", err);
      setError("Failed to load screenshots");
    } finally {
      setLoading(false);
    }
  };

  // Helper function to construct screenshot S3 key
  const constructScreenshotKey = (originalKey: string, testName: string, fileName?: string) => {
    if (!fileName) return originalKey;
    
    // Extract date from fileName (e.g., "2025-07-16T06:11:04Z-test-results.json")
    const dateMatch = fileName.match(/(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z)/);
    if (!dateMatch) return originalKey;
    
    const dateStr = dateMatch[1];
    const encodedDate = encodeURIComponent(dateStr);
    
    // Extract run ID from fileName if available
    const runIdMatch = fileName?.match(/nightly-(\d+)-/);
    const runId = runIdMatch ? runIdMatch[1] : '';
    
    // Extract screenshot name from original key
    const screenshotName = originalKey.split('/').pop() || 'screenshot.png';
    
    // Sanitize test name
    const sanitizedTestName = testName
      .replace(/[^a-zA-Z0-9 ]/g, '')
      .replace(/\s+/g, '-');
    
    // Determine if it's zgpu or guac from the test name or other info
    const runType = testName.toLowerCase().includes('zgpu') ? 'zgpu' : 'guac';
    
    // Extract spec file name - placeholder logic
    const specFile = 'banner/persistent-banner.spec.ts';
    
    return `dev/ourl-lemon/${encodedDate}-pw-originalurl-nightly-${runId}-e2e-test-*/test-results/${specFile.split('/')[0]}-${specFile.split('/')[1].replace('.spec.ts', '')}-*-${sanitizedTestName}-${runType}/${screenshotName}`;
  };

  return (
    <div>
      <Tooltip title={`View Screenshots (${screenshotUrls.length + screenshotS3Keys.length})`}>
        <Button
          type="text"
          icon={loading ? <LoadingOutlined /> : <PictureOutlined />}
          onClick={handleOpenGallery}
          disabled={!hasScreenshots}
        >
          {screenshotUrls.length + screenshotS3Keys.length}
        </Button>
      </Tooltip>

      <Modal
        title={`Screenshots: ${testName}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            Close
          </Button>,
        ]}
        width={800}
      >
        {loading && (
          <div className="flex justify-center items-center py-10">
            <LoadingOutlined style={{ fontSize: 24 }} />
            <span className="ml-2">Loading screenshots...</span>
          </div>
        )}

        {error && (
          <div className="text-red-500 text-center py-10">{error}</div>
        )}

        {!loading && !error && presignedUrls.length > 0 && (
          <div className="grid grid-cols-2 gap-4">
            <Image.PreviewGroup>
              {presignedUrls.map((url, index) => (
                <div key={index} className="border rounded overflow-hidden">
                  <Image
                    src={url}
                    alt={`Screenshot ${index + 1}`}
                    className="w-full"
                  />
                </div>
              ))}
            </Image.PreviewGroup>
          </div>
        )}

        {!loading && !error && presignedUrls.length === 0 && (
          <div className="text-center py-10">No screenshots available</div>
        )}
      </Modal>
    </div>
  );
};

export default ScreenshotGallery;
