import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Too<PERSON><PERSON> } from "antd";
import {
  PlayCircleOutlined,
  DownloadOutlined,
  LoadingOutlined,
} from "@ant-design/icons";
import apiService from "@/services/api";

interface VideoPlayerProps {
  videoUrl?: string;
  s3Key?: string;
  testName: string;
  fileName?: string; // Keep for backward compatibility but not used
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  s3Key,
  testName,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [presignedUrl, setPresignedUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleOpenVideo = async () => {
    if (!s3Key && !videoUrl) {
      setError("No video available");
      return;
    }

    setLoading(true);
    setModalVisible(true);
    setError(null);

    try {
      // If we have an S3 key, get a presigned URL
      if (s3Key) {
        const response = await apiService.getVideoURL(s3Key);
        setPresignedUrl(response.videoUrl);
      } else if (videoUrl) {
        // Use direct URL if provided
        setPresignedUrl(videoUrl);
      }
    } catch (err) {
      console.error("Failed to get video URL:", err);
      setError("Failed to load video");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Tooltip title="View Video">
        <Button
          type="text"
          icon={loading ? <LoadingOutlined /> : <PlayCircleOutlined />}
          onClick={handleOpenVideo}
          disabled={!videoUrl && !s3Key}
        />
      </Tooltip>

      <Modal
        title={`Video: ${testName}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          presignedUrl && (
            <Button
              key="download"
              icon={<DownloadOutlined />}
              href={presignedUrl}
              target="_blank"
              rel="noopener noreferrer"
            >
              Download
            </Button>
          ),
          <Button key="close" onClick={() => setModalVisible(false)}>
            Close
          </Button>,
        ]}
        width={800}
      >
        {loading && (
          <div className="flex justify-center items-center py-10">
            <LoadingOutlined style={{ fontSize: 24 }} />
            <span className="ml-2">Loading video...</span>
          </div>
        )}

        {error && <div className="text-red-500 text-center py-10">{error}</div>}

        {!loading && !error && presignedUrl && (
          <video
            controls
            autoPlay
            className="w-full"
            src={presignedUrl}
            style={{ maxHeight: "70vh" }}
          >
            Your browser does not support the video tag.
          </video>
        )}
      </Modal>
    </div>
  );
};

export default VideoPlayer;
