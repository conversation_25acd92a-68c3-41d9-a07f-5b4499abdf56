import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  DatePicker,
  Select,
  Button,
  Space,
  Tooltip,
} from "antd";
import {
  FilterOutlined,
  ClearOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import dayjs, { Dayjs } from "dayjs";

import { FilterCriteria, FilterOptions } from "@/types";
import apiService from "@/services/api";

const { RangePicker } = DatePicker;
const { Option } = Select;

interface FilterPanelProps {
  onFilterChange: (filters: FilterCriteria) => void;
  onRefresh?: () => void;
  refreshLoading?: boolean;
  initialFilters?: FilterCriteria;
}

const FilterPanel: React.FC<FilterPanelProps> = ({
  onFilterChange,
  onRefresh,
  refreshLoading = false,
  initialFilters = {},
}) => {
  const [filters, setFilters] = useState<FilterCriteria>(initialFilters);
  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    status: [],
    projectName: [],
    tags: [],
  });
  const [availableDates, setAvailableDates] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadFilterOptions();
    loadAvailableDates();
  }, []); // Load filter options and available dates on mount

  // Update filters when initialFilters prop changes
  useEffect(() => {
    setFilters(initialFilters);
  }, [initialFilters]);

  const loadFilterOptions = async () => {
    try {
      setLoading(true);
      const options = await apiService.getFilterOptions();

      // Override with Playwright-specific requirements
      const playwrightOptions: FilterOptions = {
        status: ["passed", "failed", "skipped", "flaky"], // Include flaky tests
        projectName: options.projectName || [], // Use dynamic project list (excludes coverage-setup/teardown)
        tags: options.tags || [],
      };

      setFilterOptions(playwrightOptions);
    } catch (error) {
      console.error("Failed to load filter options:", error);
      // Fallback to hardcoded options if API fails
      setFilterOptions({
        status: ["passed", "failed", "skipped", "flaky"],
        projectName: ["Guac", "zGPU"], // Updated fallback to match actual case
        tags: [],
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAvailableDates = async () => {
    try {
      const dates = await apiService.getAvailableDates();
      setAvailableDates(dates);
    } catch (error) {
      console.error("Failed to load available dates:", error);
      setAvailableDates([]);
    }
  };

  const handleDateRangeChange = (
    dates: [Dayjs | null, Dayjs | null] | null
  ) => {
    const newFilters = { ...filters };

    if (dates && dates.length === 2 && dates[0] && dates[1]) {
      newFilters.startDate = dates[0].format("YYYY-MM-DD");
      newFilters.endDate = dates[1].format("YYYY-MM-DD");
    } else {
      delete newFilters.startDate;
      delete newFilters.endDate;
    }

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleTodayClick = () => {
    const today = dayjs();
    const newFilters = { ...filters };
    newFilters.startDate = today.format("YYYY-MM-DD");
    newFilters.endDate = today.format("YYYY-MM-DD");

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleThisWeekClick = () => {
    const today = dayjs();
    const startOfWeek = today.startOf("week");
    const newFilters = { ...filters };
    newFilters.startDate = startOfWeek.format("YYYY-MM-DD");
    newFilters.endDate = today.format("YYYY-MM-DD");

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleThisMonthClick = () => {
    const today = dayjs();
    const startOfMonth = today.startOf("month");
    const newFilters = { ...filters };
    newFilters.startDate = startOfMonth.format("YYYY-MM-DD");
    newFilters.endDate = today.format("YYYY-MM-DD");

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleSelectChange = (
    field: "status" | "projectName" | "filePath" | "tags",
    value: string[]
  ) => {
    const newFilters = { ...filters };

    if (value && value.length > 0) {
      newFilters[field] = value;
    } else {
      delete newFilters[field];
    }

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleDateSelectChange = (value: string) => {
    const newFilters = { ...filters };

    if (value) {
      newFilters.selectedDate = value;
    } else {
      delete newFilters.selectedDate;
    }

    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({});
    onFilterChange({});
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        title={
          <Space>
            <FilterOutlined />
            <span>Filters</span>
            {hasActiveFilters && (
              <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                {Object.keys(filters).length} active
              </span>
            )}
          </Space>
        }
        extra={
          <Space>
            {onRefresh && (
              <Tooltip title="Refresh Data">
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={onRefresh}
                  loading={refreshLoading}
                />
              </Tooltip>
            )}
            {hasActiveFilters && (
              <Button
                type="text"
                icon={<ClearOutlined />}
                onClick={handleClearFilters}
                size="small"
              >
                Clear All
              </Button>
            )}
          </Space>
        }
        className="filter-section"
        styles={{
          body: { padding: "12px 16px" },
          header: { padding: "8px 16px", minHeight: "40px" },
        }}
      >
        <Row gutter={[12, 8]}>
          {/* Date Range Filter */}
          <Col xs={24} sm={12} md={6}>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Date Range
              </label>
              <RangePicker
                className="w-full mb-1"
                size="small"
                onChange={handleDateRangeChange}
                value={
                  filters.startDate && filters.endDate
                    ? [dayjs(filters.startDate), dayjs(filters.endDate)]
                    : null
                }
                placeholder={["Start Date", "End Date"]}
                disabledDate={(current) => {
                  // Disable dates after today (end of current day)
                  return current && current.isAfter(dayjs(), "day");
                }}
              />
              <Space size={4} className="w-full" wrap>
                <Button
                  size="small"
                  onClick={handleTodayClick}
                  title="Current Date - Navigate back to today"
                  className="text-xs px-2 py-0"
                >
                  Current Date
                </Button>
                <Button
                  size="small"
                  onClick={handleThisWeekClick}
                  title="This Week"
                  className="text-xs px-2 py-0"
                >
                  Week
                </Button>
                <Button
                  size="small"
                  onClick={handleThisMonthClick}
                  title="This Month"
                  className="text-xs px-2 py-0"
                >
                  Month
                </Button>
              </Space>
            </div>
          </Col>

          {/* Status Filter */}
          <Col xs={24} sm={12} md={6}>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Test Status
              </label>
              <Select
                mode="multiple"
                className="w-full"
                placeholder="Select status"
                value={filters.status || []}
                onChange={(value) => handleSelectChange("status", value)}
                loading={loading}
                allowClear
                size="small"
                getPopupContainer={(trigger) =>
                  trigger.parentElement || document.body
                }
              >
                {filterOptions.status.map((status) => (
                  <Option key={status} value={status}>
                    {status === "passed" && "✅ "}
                    {status === "failed" && "❌ "}
                    {status === "skipped" && "⏭️ "}
                    {status === "flaky" && "🔄 "}
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          {/* Project Filter */}
          <Col xs={24} sm={12} md={6}>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Project
              </label>
              <Select
                mode="multiple"
                className="w-full"
                placeholder="Select project"
                value={filters.projectName || []}
                onChange={(value) => handleSelectChange("projectName", value)}
                loading={loading}
                allowClear
                size="small"
                getPopupContainer={(trigger) =>
                  trigger.parentElement || document.body
                }
              >
                {filterOptions.projectName.map((project) => (
                  <Option key={project} value={project}>
                    {project}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          {/* Nightly Run Date Filter */}
          <Col xs={24} sm={12} md={6}>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Nightly Run
              </label>
              <Select
                className="w-full"
                placeholder="Select date"
                value={filters.selectedDate || undefined}
                onChange={handleDateSelectChange}
                allowClear
                size="small"
                getPopupContainer={(trigger) =>
                  trigger.parentElement || document.body
                }
              >
                {availableDates.map((date) => (
                  <Option key={date} value={date}>
                    {new Date(date).toLocaleDateString("en-US", {
                      weekday: "short",
                      year: "numeric",
                      month: "short",
                      day: "numeric",
                    })}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          {/* Tags Filter */}
          <Col xs={24}>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Tags
              </label>
              <Select
                mode="multiple"
                className="w-full"
                placeholder="Select tags"
                value={filters.tags || []}
                onChange={(value) => handleSelectChange("tags", value)}
                loading={loading}
                allowClear
                maxTagCount="responsive"
                size="small"
                getPopupContainer={(trigger) =>
                  trigger.parentElement || document.body
                }
              >
                {filterOptions.tags.map((tag) => (
                  <Option key={tag} value={tag}>
                    {tag}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
        </Row>

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <motion.div
            className="mt-2 pt-2 border-t border-gray-200"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-xs text-gray-600">
              <span className="font-medium">Active:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {filters.startDate && filters.endDate && (
                  <span className="bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded text-xs">
                    📅 {filters.startDate} to {filters.endDate}
                  </span>
                )}
                {filters.status?.map((status) => (
                  <span
                    key={status}
                    className="bg-green-100 text-green-700 px-1.5 py-0.5 rounded text-xs"
                  >
                    📊 {status}
                  </span>
                ))}
                {filters.projectName?.map((project) => (
                  <span
                    key={project}
                    className="bg-purple-100 text-purple-700 px-1.5 py-0.5 rounded text-xs"
                  >
                    🏗️ {project}
                  </span>
                ))}
                {filters.tags?.map((tag) => (
                  <span
                    key={tag}
                    className="bg-gray-100 text-gray-700 px-1.5 py-0.5 rounded text-xs"
                  >
                    🏷️ {tag}
                  </span>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </Card>
    </motion.div>
  );
};

export default FilterPanel;
