import React from "react";
import { Layout, Menu } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import {
  DashboardOutlined,
  BarChartOutlined,
  FileTextOutlined,
  LineChartOutlined,
  UnorderedListOutlined,
  CloudOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";

const { Sider } = Layout;

interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ collapsed, onCollapse }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: "/dashboard",
      icon: <DashboardOutlined />,
      label: "Dashboard",
      path: "/dashboard",
    },
    {
      key: "/analytics",
      icon: <BarChartOutlined />,
      label: "Test Analytics",
      path: "/analytics",
    },
    {
      key: "/test-cases",
      icon: <UnorderedListOutlined />,
      label: "Test Cases",
      path: "/test-cases",
    },

    {
      key: "/trends",
      icon: <LineChartOutlined />,
      label: "Trends & Insights",
      path: "/trends",
    },
    {
      key: "/files",
      icon: <FileTextOutlined />,
      label: "File Management",
      path: "/files",
    },
    {
      key: "/s3-config",
      icon: <CloudOutlined />,
      label: "S3 Integration",
      path: "/s3-config",
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={onCollapse}
      width={256}
      collapsedWidth={80}
      className="fixed left-0 z-50"
      style={{
        background:
          "linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%)",
        boxShadow: "2px 0 8px rgba(0, 0, 0, 0.15)",
        zIndex: 1000,
        top: 64,
        height: "calc(100vh - 64px)",
      }}
    >
      {/* Navigation Menu */}
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        onClick={handleMenuClick}
        className="border-none"
        style={{
          background: "transparent",
          fontSize: "14px",
        }}
        items={menuItems.map((item, index) => ({
          ...item,
          label: (
            <motion.span
              initial={{ opacity: 0, x: -10 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 + 0.3 }}
            >
              {item.label}
            </motion.span>
          ),
        }))}
      />

      {/* Footer Section */}
      {!collapsed && (
        <motion.div
          className="absolute bottom-4 left-4 right-4 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <div className="text-xs text-blue-100 border-t border-blue-400/30 pt-4">
            <div className="mb-1">Playwright Analytics</div>
            <div className="text-white font-semibold">v1.0.0</div>
          </div>
        </motion.div>
      )}

      {/* Collapsed Footer */}
      {collapsed && (
        <motion.div
          className="absolute bottom-4 left-0 right-0 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <div className="text-xs text-white font-bold">v1.0</div>
        </motion.div>
      )}
    </Sider>
  );
};

export default Sidebar;
