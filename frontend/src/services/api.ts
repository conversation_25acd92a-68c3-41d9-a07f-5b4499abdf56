import axios, { AxiosInstance, AxiosResponse } from "axios";
import toast from "react-hot-toast";
import {
  DashboardMetrics,
  TestAnalytics,
  TestRun,
  ChartData,
  WordCloudData,
  SunburstData,
  BubbleChartData,
  HeatmapData,
  FilterCriteria,
  FilterOptions,
  FileInfo,
  UploadResponse,
  MultipleUploadResponse,
  JSONUploadRequest,
  JSONUploadResponse,
  SlackNotificationRequest,
  SlackNotificationResponse,
  ErrorCategoryDetail,
} from "@/types";

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "/api";

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        "Content-Type": "application/json",
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        // Add any auth headers here if needed
        return config;
      },
      (error) => {
        console.error("❌ Request Error:", error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        console.error("❌ Response Error:", error);

        // Check if this is a video URL request - don't show toast for these
        const isVideoRequest = error.config?.url?.includes("/v1/videos/");
        const isScreenshotRequest =
          error.config?.url?.includes("/v1/screenshots/");

        // Handle different error types
        if (error.response) {
          // Server responded with error status
          const { status, data } = error.response;
          const message =
            data?.error || data?.message || `HTTP ${status} Error`;

          // Don't show toast notifications for video/screenshot 404 errors
          if ((isVideoRequest || isScreenshotRequest) && status === 404) {
            return Promise.reject(error);
          }

          switch (status) {
            case 400:
              toast.error(`Bad Request: ${message}`);
              break;
            case 401:
              toast.error("Unauthorized access");
              break;
            case 403:
              toast.error("Access forbidden");
              break;
            case 404:
              toast.error("Resource not found");
              break;
            case 500:
              toast.error("Internal server error");
              break;
            default:
              toast.error(`Error: ${message}`);
          }
        } else if (error.request) {
          // Network error
          toast.error("Network error - please check your connection");
        } else {
          // Other error
          toast.error("An unexpected error occurred");
        }

        return Promise.reject(error);
      }
    );
  }

  // Helper method to build query string from filters
  private buildQueryString(filters?: FilterCriteria): string {
    if (!filters) return "";

    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach((v) => params.append(key, v));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    return params.toString();
  }

  // Analytics API methods
  async getDashboardMetrics(
    filters?: FilterCriteria
  ): Promise<DashboardMetrics> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/dashboard${queryString ? `?${queryString}` : ""}`;
    const response = await this.api.get<DashboardMetrics>(url);
    return response.data;
  }

  async getTestAnalytics(filters?: FilterCriteria): Promise<TestAnalytics[]> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/tests${queryString ? `?${queryString}` : ""}`;
    console.log("🌐 API Service: Making request to:", url);
    console.log("🔍 API Service: Filters:", JSON.stringify(filters, null, 2));
    const response = await this.api.get<TestAnalytics[]>(url);
    console.log(
      "📊 API Service: Received",
      response.data.length,
      "test records"
    );
    return response.data;
  }

  async getErrorCategoriesWithTests(
    filters?: FilterCriteria
  ): Promise<ErrorCategoryDetail[]> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/error-categories${
      queryString ? `?${queryString}` : ""
    }`;
    const response = await this.api.get<ErrorCategoryDetail[]>(url);
    return response.data;
  }

  async getTestRuns(filters?: FilterCriteria): Promise<TestRun[]> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/test-runs${queryString ? `?${queryString}` : ""}`;
    const response = await this.api.get<TestRun[]>(url);
    return response.data;
  }

  async getChartData(
    chartType: string,
    filters?: FilterCriteria
  ): Promise<ChartData> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/charts/${chartType}${
      queryString ? `?${queryString}` : ""
    }`;
    const response = await this.api.get<ChartData>(url);
    return response.data;
  }

  async getWordCloudData(filters?: FilterCriteria): Promise<WordCloudData[]> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/wordcloud${queryString ? `?${queryString}` : ""}`;
    const response = await this.api.get<WordCloudData[]>(url);
    return response.data;
  }

  async getSunburstData(filters?: FilterCriteria): Promise<SunburstData> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/sunburst${queryString ? `?${queryString}` : ""}`;
    const response = await this.api.get<SunburstData>(url);
    return response.data;
  }

  async getBubbleChartData(
    filters?: FilterCriteria
  ): Promise<BubbleChartData[]> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/bubble${queryString ? `?${queryString}` : ""}`;
    const response = await this.api.get<BubbleChartData[]>(url);
    return response.data;
  }

  async getHeatmapData(filters?: FilterCriteria): Promise<HeatmapData[]> {
    const queryString = this.buildQueryString(filters);
    const url = `/analytics/heatmap${queryString ? `?${queryString}` : ""}`;
    const response = await this.api.get<HeatmapData[]>(url);
    return response.data;
  }

  async getFilterOptions(): Promise<FilterOptions> {
    const response = await this.api.get<FilterOptions>(
      "/analytics/filter-options"
    );
    return response.data;
  }

  async getAvailableDates(): Promise<string[]> {
    const response = await this.api.get<{ dates: string[] }>(
      "/analytics/available-dates"
    );
    return response.data.dates;
  }

  // File management API methods
  async getFiles(): Promise<{ files: FileInfo[]; count: number }> {
    const response = await this.api.get<{ files: FileInfo[]; count: number }>(
      "/files"
    );
    return response.data;
  }

  async uploadFile(file: File): Promise<UploadResponse> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await this.api.post<UploadResponse>(
      "/files/upload",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            console.log(`Upload progress: ${percentCompleted}%`);
          }
        },
      }
    );

    toast.success(`File "${file.name}" uploaded successfully!`);
    return response.data;
  }

  async uploadMultipleFiles(files: File[]): Promise<MultipleUploadResponse> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("files", file);
    });

    const response = await this.api.post<MultipleUploadResponse>(
      "/files/upload-multiple",
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            console.log(`Upload progress: ${percentCompleted}%`);
          }
        },
      }
    );

    const { uploadedCount, totalFiles, errorCount = 0 } = response.data;

    if (errorCount > 0) {
      toast.error(`${errorCount} files failed to upload`);
    }

    if (uploadedCount > 0) {
      toast.success(`${uploadedCount} files uploaded successfully!`);
    }

    return response.data;
  }

  async uploadJSONText(
    request: JSONUploadRequest
  ): Promise<JSONUploadResponse> {
    const response = await this.api.post<JSONUploadResponse>(
      "/files/upload-json-text",
      request,
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.valid) {
      toast.success(
        `JSON content uploaded successfully as "${response.data.fileName}"!`
      );
    }

    return response.data;
  }

  async deleteFile(fileName: string): Promise<void> {
    await this.api.delete(`/files/${fileName}`);
    toast.success(`File "${fileName}" deleted successfully!`);
  }

  async getFileStats(): Promise<{
    totalFiles: number;
    fileTypes: Record<string, number>;
  }> {
    const response = await this.api.get<{
      totalFiles: number;
      fileTypes: Record<string, number>;
    }>("/files/stats");
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<{
    status: string;
    service: string;
    version: string;
  }> {
    const response = await this.api.get<{
      status: string;
      service: string;
      version: string;
    }>("/health");
    return response.data;
  }

  // Slack notification methods
  async sendSlackNotification(
    request: SlackNotificationRequest
  ): Promise<SlackNotificationResponse> {
    const response = await this.api.post<SlackNotificationResponse>(
      "/slack/notify",
      request
    );
    toast.success("Slack notification sent successfully!");
    return response.data;
  }

  async testSlackConnection(): Promise<{ message: string }> {
    const response = await this.api.get<{ message: string }>("/slack/test");
    toast.success("Test message sent to Slack!");
    return response.data;
  }

  // S3 Integration methods
  async getVideoURL(
    s3Key: string
  ): Promise<{ videoUrl: string; expiresIn: string; s3Key: string }> {
    const response = await this.api.get<{
      videoUrl: string;
      expiresIn: string;
      s3Key: string;
    }>(`/v1/videos/${encodeURIComponent(s3Key)}`);
    return response.data;
  }

  async getScreenshotURL(
    s3Key: string
  ): Promise<{ screenshotUrl: string; expiresIn: string; s3Key: string }> {
    const response = await this.api.get<{
      screenshotUrl: string;
      expiresIn: string;
      s3Key: string;
    }>(`/v1/screenshots/${encodeURIComponent(s3Key)}`);
    return response.data;
  }

  async getAttachmentURL(
    s3Key: string
  ): Promise<{ attachmentUrl: string; expiresIn: string; s3Key: string }> {
    const response = await this.api.get<{
      attachmentUrl: string;
      expiresIn: string;
      s3Key: string;
    }>(`/v1/attachments/${encodeURIComponent(s3Key)}`);
    return response.data;
  }

  async getS3Status(): Promise<{
    bucket?: string;
    connected: boolean;
    schedulerRunning?: boolean;
    processedFiles?: number;
    timestamp?: string;
    s3Enabled: boolean;
    message?: string;
  }> {
    const response = await this.api.get("/v1/s3/status");
    return response.data;
  }

  async triggerS3Sync(): Promise<{ message: string }> {
    const response = await this.api.post<{ message: string }>("/v1/s3/sync");
    toast.success("S3 sync triggered successfully!");
    return response.data;
  }

  async clearS3Cache(): Promise<{ message: string }> {
    const response = await this.api.post<{ message: string }>(
      "/v1/s3/clear-cache"
    );
    toast.success("S3 cache cleared successfully!");
    return response.data;
  }

  // Chatbot methods
  async sendChatMessage(
    message: string,
    context?: any,
    filters?: any
  ): Promise<{
    message: string;
    data?: any;
    suggestions?: string[];
    chartData?: any;
    timestamp: string;
    success: boolean;
  }> {
    const response = await this.api.post("/v1/chatbot/chat", {
      message,
      context: context || {},
      filters: filters || {},
    });
    return response.data;
  }

  async getChatSuggestions(): Promise<{
    suggestions: string[];
    success: boolean;
  }> {
    const response = await this.api.get("/v1/chatbot/suggestions");
    return response.data;
  }

  async getChatStatus(): Promise<{
    available: boolean;
    message: string;
    dataPoints: number;
    testFiles: number;
    lastUpdated: string;
    success: boolean;
  }> {
    const response = await this.api.get("/v1/chatbot/status");
    return response.data;
  }

  async getChatHistory(): Promise<{ history: any[]; success: boolean }> {
    const response = await this.api.get("/v1/chatbot/history");
    return response.data;
  }

  // Utility methods
  async testConnection(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      console.error("Connection test failed:", error);
      return false;
    }
  }

  // Chart type helpers
  getAvailableChartTypes(): string[] {
    return [
      "pass-fail-trend",
      "test-duration",
      "status-distribution",
      "project-breakdown",
      "error-categories",
    ];
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;

// Export individual methods for convenience
export const {
  getDashboardMetrics,
  getTestAnalytics,
  getTestRuns,
  getErrorCategoriesWithTests,
  getChartData,
  getWordCloudData,
  getSunburstData,
  getBubbleChartData,
  getHeatmapData,
  getFilterOptions,
  getAvailableDates,
  getFiles,
  uploadFile,
  uploadMultipleFiles,
  uploadJSONText,
  deleteFile,
  getFileStats,
  healthCheck,
  testConnection,
  getAvailableChartTypes,
  sendSlackNotification,
  testSlackConnection,
  getVideoURL,
  getScreenshotURL,
  getAttachmentURL,
  getS3Status,
  triggerS3Sync,
  clearS3Cache,
  sendChatMessage,
  getChatSuggestions,
  getChatStatus,
  getChatHistory,
} = apiService;

// Add method to get test runs by file
export const getTestRunsByFile = async (
  fileName: string
): Promise<TestRun[]> => {
  const response = await fetch(
    `${API_BASE_URL}/analytics/test-runs?file=${encodeURIComponent(fileName)}`
  );
  if (!response.ok) {
    throw new Error(`Failed to fetch test runs for file: ${fileName}`);
  }
  const data = await response.json();
  return data.testRuns || [];
};
