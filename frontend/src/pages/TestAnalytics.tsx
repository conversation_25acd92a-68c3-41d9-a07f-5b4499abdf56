import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Table, Card, Input, Tag, Tooltip, Button } from "antd";
import {
  SearchOutlined,
  FileTextOutlined,
  FileExcelOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import { useSearchParams, useNavigate } from "react-router-dom";
import toast from "react-hot-toast";

// Components
import FilterPanel from "@/components/filters/FilterPanel";
import AttachmentsViewer from "@/components/common/AttachmentsViewer";

// Services & Types
import apiService from "@/services/api";
import { TestAnalytics as TestAnalyticsType, FilterCriteria } from "@/types";
import { ColumnType } from "antd/es/table";

// Utils
import { exportFilteredTestAnalytics } from "@/utils/excelExport";

const { Search } = Input;

const TestAnalytics: React.FC = () => {
  const [tests, setTests] = useState<TestAnalyticsType[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState("");
  const [filters, setFilters] = useState<FilterCriteria>({});
  const [filtersInitialized, setFiltersInitialized] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Initialize filters from URL parameters on mount and when URL changes
  useEffect(() => {
    const urlFilters: FilterCriteria = {};

    // Parse status parameter(s)
    const statusParams = searchParams.getAll("status");
    if (statusParams.length > 0) {
      urlFilters.status = statusParams;
    }

    // Parse projectName parameter(s)
    const projectNameParams = searchParams.getAll("projectName");
    if (projectNameParams.length > 0) {
      urlFilters.projectName = projectNameParams;
    }

    // Parse filePath parameter(s)
    const filePathParams = searchParams.getAll("filePath");
    if (filePathParams.length > 0) {
      urlFilters.filePath = filePathParams;
    }

    // Parse tags parameter(s)
    const tagsParams = searchParams.getAll("tags");
    if (tagsParams.length > 0) {
      urlFilters.tags = tagsParams;
    }

    // Parse date parameters
    const startDate = searchParams.get("startDate");
    if (startDate) {
      urlFilters.startDate = startDate;
    }

    const endDate = searchParams.get("endDate");
    if (endDate) {
      urlFilters.endDate = endDate;
    }

    console.log("🔍 Parsed URL filters:", urlFilters);

    // Always set filters (even if empty) to ensure consistency
    setFilters(urlFilters);
    setFiltersInitialized(true);
  }, [searchParams]);

  const loadTestData = useCallback(
    async (showSuccessToast: boolean = false) => {
      // Prevent multiple simultaneous API calls
      if (isLoadingData) {
        console.log("⏳ API call already in progress, skipping...");
        return;
      }

      try {
        // Only show loading for initial load or when explicitly refreshing
        if (tests.length === 0 || showSuccessToast) {
          setLoading(true);
        }
        setIsLoadingData(true);
        console.log(
          "🚀 Making API call with filters:",
          JSON.stringify(filters, null, 2)
        );
        const data = await apiService.getTestAnalytics(filters);
        console.log("📈 Received test data:", data.length, "tests");
        setTests(data);
        if (showSuccessToast) {
          toast.success("Test analytics loaded successfully");
        }
      } catch (error) {
        console.error("❌ Failed to load test analytics:", error);
        toast.error("Failed to load test analytics");
      } finally {
        setLoading(false);
        setIsLoadingData(false);
      }
    },
    [filters, isLoadingData]
  );

  // Load data whenever filters change, but only after filters are initialized
  useEffect(() => {
    if (filtersInitialized && !isLoadingData) {
      console.log("📊 Loading test data with filters:", filters);
      // Add a small delay to ensure filters are fully set
      const timeoutId = setTimeout(() => {
        loadTestData();
      }, 50); // Reduced delay for better responsiveness

      return () => clearTimeout(timeoutId);
    }
  }, [filters, filtersInitialized]); // Removed isLoadingData and loadTestData to prevent loops

  const handleFilterChange = (newFilters: FilterCriteria) => {
    setFilters(newFilters);

    // Update URL parameters to reflect the new filters
    const searchParams = new URLSearchParams();

    if (newFilters.startDate) {
      searchParams.set("startDate", newFilters.startDate);
    }
    if (newFilters.endDate) {
      searchParams.set("endDate", newFilters.endDate);
    }
    if (newFilters.status && newFilters.status.length > 0) {
      newFilters.status.forEach((status) => {
        searchParams.append("status", status);
      });
    }
    if (newFilters.projectName && newFilters.projectName.length > 0) {
      newFilters.projectName.forEach((project) => {
        searchParams.append("projectName", project);
      });
    }
    if (newFilters.filePath && newFilters.filePath.length > 0) {
      newFilters.filePath.forEach((path) => {
        searchParams.append("filePath", path);
      });
    }
    if (newFilters.tags && newFilters.tags.length > 0) {
      newFilters.tags.forEach((tag) => {
        searchParams.append("tags", tag);
      });
    }

    // Update the URL without causing a page reload
    const newUrl = `/analytics${
      searchParams.toString() ? `?${searchParams.toString()}` : ""
    }`;
    navigate(newUrl, { replace: true });
  };

  const handleRefresh = () => {
    loadTestData(true); // Show toast when explicitly refreshing
  };

  const handlePaginationChange = useCallback((page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  }, []);

  const handleSearchChange = useCallback((value: string) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const handleExportToExcel = () => {
    try {
      const result = exportFilteredTestAnalytics(
        filteredTests,
        {
          searchText,
          appliedFilters: filters,
        },
        "test-analytics"
      );

      if (result.success) {
        toast.success(
          `Successfully exported ${result.recordCount} test records to ${result.filename}`
        );
      } else {
        toast.error(`Export failed: ${result.error}`);
      }
    } catch (error) {
      toast.error("Failed to export data to Excel");
      console.error("Export error:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "passed":
        return "green";
      case "failed":
        return "red";
      case "flaky":
        return "orange";
      case "skipped":
        return "gray";
      default:
        return "default";
    }
  };

  // Determine test status based on pass/fail/flaky rates
  const determineTestStatus = (test: TestAnalyticsType): string => {
    const { passRate, failRate, flakyRate, skippedRate } = test;

    // If there's any flaky rate from backend, it's flaky
    if (flakyRate > 0) {
      return "flaky";
    }

    // If both pass rate and fail rate are > 0%, it's flaky
    if (passRate > 0 && failRate > 0) {
      return "flaky";
    }

    // If only pass rate > 0%, it's passed
    if (passRate > 0 && failRate === 0) {
      return "passed";
    }

    // If only fail rate > 0%, it's failed
    if (failRate > 0 && passRate === 0) {
      return "failed";
    }

    // If only skipped rate > 0%, it's skipped
    if (skippedRate > 0 && passRate === 0 && failRate === 0) {
      return "skipped";
    }

    // Default fallback
    return "unknown";
  };

  // Get the effective pass rate (includes flaky tests that eventually passed)
  const getEffectivePassRate = (test: TestAnalyticsType): number => {
    // If there's flaky rate, those should be considered as partial passes
    // For display purposes, show pass rate + flaky rate as the "success" rate
    return test.passRate + test.flakyRate;
  };

  const formatDuration = (duration: number): string => {
    const seconds = duration / 1000;
    if (seconds < 60) {
      return `${seconds.toFixed(1)}s`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
    }
  };

  const columns: ColumnType<TestAnalyticsType>[] = useMemo(
    () => [
      {
        title: "S.No",
        dataIndex: "serialNumber",
        key: "serialNumber",
        width: 80,
        render: (_: any, __: any, index: number) =>
          (currentPage - 1) * pageSize + index + 1,
        sorter: (a: any, b: any) => a.serialNumber - b.serialNumber,
      },
      {
        title: "Test Name",
        dataIndex: "testName",
        key: "testName",
        width: 300,
        // Test names are displayed in 'suites.suites.title > specs.title' format
        // This shows describe name followed by test name as per requirements
        render: (text: string, record: TestAnalyticsType) => (
          <div>
            <div className="font-medium text-gray-900 truncate" title={text}>
              {text}
            </div>
            <div className="flex items-center space-x-1 mt-1">
              <FileTextOutlined className="text-xs text-gray-400" />
              <span
                className="text-xs text-gray-500 truncate"
                title={record.filePath}
              >
                {record.filePath}
              </span>
            </div>
          </div>
        ),
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          a.testName.localeCompare(b.testName),
      },
      {
        title: (
          <Tooltip title="Effective pass rate includes both pure passes and flaky tests that eventually passed">
            Pass Rate
          </Tooltip>
        ),
        dataIndex: "passRate",
        key: "passRate",
        width: 120,
        render: (_: number, record: TestAnalyticsType) => {
          const effectivePassRate = getEffectivePassRate(record);
          const purePassRate = record.passRate;
          const flakyRate = record.flakyRate;

          return (
            <div className="text-center">
              <div
                className={`font-semibold ${
                  effectivePassRate >= 90
                    ? "text-green-600"
                    : effectivePassRate >= 70
                    ? "text-orange-600"
                    : "text-red-600"
                }`}
              >
                {effectivePassRate.toFixed(1)}%
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                <div className="relative h-2 rounded-full overflow-hidden">
                  {/* Pure pass rate in green */}
                  <div
                    className="absolute left-0 top-0 h-full bg-green-500"
                    style={{ width: `${purePassRate}%` }}
                  />
                  {/* Flaky rate in orange, positioned after pass rate */}
                  {flakyRate > 0 && (
                    <div
                      className="absolute top-0 h-full bg-orange-500"
                      style={{
                        left: `${purePassRate}%`,
                        width: `${flakyRate}%`,
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          );
        },
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          getEffectivePassRate(a) - getEffectivePassRate(b),
      },
      {
        title: "Fail Rate",
        dataIndex: "failRate",
        key: "failRate",
        width: 120,
        render: (rate: number) => (
          <div className="text-center">
            <div
              className={`font-semibold ${
                rate === 0
                  ? "text-green-600"
                  : rate <= 10
                  ? "text-orange-600"
                  : "text-red-600"
              }`}
            >
              {rate.toFixed(1)}%
            </div>
          </div>
        ),
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          a.failRate - b.failRate,
        defaultSortOrder: "descend" as const,
      },

      {
        title: "Skipped Rate",
        dataIndex: "skippedRate",
        key: "skippedRate",
        width: 120,
        render: (rate: number) => (
          <div className="text-center">
            <div
              className={`font-semibold ${
                rate === 0
                  ? "text-green-600"
                  : rate <= 10
                  ? "text-orange-600"
                  : "text-gray-600"
              }`}
            >
              {rate.toFixed(1)}%
            </div>
          </div>
        ),
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          a.skippedRate - b.skippedRate,
      },
      {
        title: "Total Runs",
        dataIndex: "totalRuns",
        key: "totalRuns",
        width: 100,
        render: (runs: number) => <span className="font-medium">{runs}</span>,
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          a.totalRuns - b.totalRuns,
      },
      {
        title: "Avg Duration",
        dataIndex: "averageDuration",
        key: "averageDuration",
        width: 120,
        render: (duration: number) => (
          <Tooltip title={`${duration.toFixed(0)}ms`}>
            <span
              className={`font-medium ${
                duration > 60000
                  ? "text-red-600"
                  : duration > 30000
                  ? "text-orange-600"
                  : "text-green-600"
              }`}
            >
              {formatDuration(duration)}
            </span>
          </Tooltip>
        ),
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          a.averageDuration - b.averageDuration,
      },

      {
        title: "Project",
        dataIndex: "projectName",
        key: "projectName",
        width: 120,
        render: (project: string) => <Tag color="blue">{project}</Tag>,
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) =>
          a.projectName.localeCompare(b.projectName),
      },
      {
        title: "Status",
        dataIndex: "lastStatus",
        key: "status",
        width: 100,
        render: (_: string, record: TestAnalyticsType) => {
          const status = determineTestStatus(record);
          return (
            <Tag color={getStatusColor(status)}>
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Tag>
          );
        },
        sorter: (a: TestAnalyticsType, b: TestAnalyticsType) => {
          const statusA = determineTestStatus(a);
          const statusB = determineTestStatus(b);
          return statusA.localeCompare(statusB);
        },
      },
      {
        title: "Tags",
        dataIndex: "tags",
        key: "tags",
        width: 200,
        render: (tags: string[]) => (
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 3).map((tag) => (
              <Tag key={tag} className="text-xs">
                {tag}
              </Tag>
            ))}
            {tags.length > 3 && (
              <Tooltip title={tags.slice(3).join(", ")}>
                <Tag className="text-xs">+{tags.length - 3}</Tag>
              </Tooltip>
            )}
          </div>
        ),
      },
      {
        title: "Attachments",
        key: "attachments",
        width: 120,
        render: (_: any, record: TestAnalyticsType) => (
          <AttachmentsViewer
            attachments={record.allAttachmentS3Keys}
            testName={record.testName}
          />
        ),
      },
    ],
    [currentPage, pageSize]
  );

  // First deduplicate tests to avoid duplicate keys
  const deduplicatedTests = useMemo(() => {
    const seen = new Set();
    const unique = [];

    for (const test of tests) {
      const key = `${test.testName}-${test.filePath}-${test.projectName}`;
      if (!seen.has(key)) {
        seen.add(key);
        unique.push(test);
      } else {
        console.log("🔄 Duplicate test found:", key);
      }
    }

    console.log(
      `📊 Original tests: ${tests.length}, Unique tests: ${unique.length}`
    );
    return unique;
  }, [tests]);

  // Filter tests based on search text and sort by fail rate (descending) by default
  const filteredTests = useMemo(() => {
    if (!searchText.trim()) {
      return deduplicatedTests.sort((a, b) => b.failRate - a.failRate);
    }

    const searchLower = searchText.toLowerCase().trim();
    console.log("🔍 Searching for:", `"${searchLower}"`);

    const filtered = deduplicatedTests.filter((test) => {
      const testNameLower = test.testName.toLowerCase();
      const filePathLower = test.filePath.toLowerCase();
      const projectNameLower = test.projectName.toLowerCase();

      // Exact match only - check if the search term exactly matches:
      // 1. The complete test name
      // 2. The complete file path
      // 3. The complete project name
      // 4. Any complete segment after " > " delimiter in test names

      // Check for exact matches in full fields
      if (testNameLower === searchLower) {
        console.log("✅ Exact match found in test name:", test.testName);
        return true;
      }

      if (filePathLower === searchLower) {
        console.log("✅ Exact match found in file path:", test.filePath);
        return true;
      }

      if (projectNameLower === searchLower) {
        console.log("✅ Exact match found in project name:", test.projectName);
        return true;
      }

      // Check for exact segment matches in test names (after " > " delimiter)
      const testNameSegments = test.testName.split(" > ");

      for (const segment of testNameSegments) {
        const segmentLower = segment.toLowerCase().trim();
        if (segmentLower === searchLower) {
          console.log(
            "✅ Exact segment match found:",
            segment,
            "in test:",
            test.testName
          );
          return true;
        }
      }

      return false;
    });

    console.log(`🎯 Filtered results: ${filtered.length} tests found`);
    return filtered.sort((a, b) => b.failRate - a.failRate); // Default sort by fail rate descending
  }, [deduplicatedTests, searchText]);

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Filters */}
      <FilterPanel
        key="analytics-filters" // Force fresh instance for analytics
        onFilterChange={handleFilterChange}
        onRefresh={handleRefresh}
        refreshLoading={loading}
        initialFilters={filters}
      />

      {/* Search and Table */}
      <Card>
        <div className="mb-4" style={{ width: "100%" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <Search
                placeholder="Search tests, files, or projects..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                value={searchText}
                onChange={(e) => handleSearchChange(e.target.value)}
                onSearch={handleSearchChange}
                style={{ width: 400 }}
              />
              <span className="text-gray-500" style={{ whiteSpace: "nowrap" }}>
                Showing {filteredTests.length} of {tests.length} tests
              </span>
            </div>
            <Button
              type="primary"
              icon={<FileExcelOutlined />}
              onClick={handleExportToExcel}
              disabled={filteredTests.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
              style={{ flexShrink: 0 }}
            >
              Export to Excel
            </Button>
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={filteredTests}
          loading={loading}
          rowKey={(record) =>
            `${record.testName}-${record.filePath}-${record.projectName}`
          }
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ["10", "20", "50", "100", "200"],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} tests`,
            onChange: handlePaginationChange,
            onShowSizeChange: handlePaginationChange,
            size: "default",
          }}
          scroll={{ x: 1440 }}
          size="small"
        />
      </Card>
    </motion.div>
  );
};

export default TestAnalytics;
