import React, { useState, useEffect } from "react";
import { Card, Row, Col, <PERSON>, Button, Alert } from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import apiService from "@/services/api";
import SlackNotification from "@/components/SlackNotification";

interface SimpleMetrics {
  totalTests: number;
  overallPassRate: number;
  overallFailRate: number;
  overallFlakyRate: number;
  overallSkipRate: number;
}

const SimpleDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<SimpleMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await apiService.getDashboardMetrics();

      setMetrics({
        totalTests: data.totalTests,
        overallPassRate: data.overallPassRate,
        overallFailRate: data.overallFailRate,
        overallFlakyRate: data.overallFlakyRate,
        overallSkipRate: data.overallSkipRate,
      });
    } catch (err) {
      console.error("❌ Error loading dashboard data:", err);
      setError(err instanceof Error ? err.message : "Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  if (error) {
    return (
      <Alert
        message="Error Loading Dashboard"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={loadData} type="primary">
            Retry
          </Button>
        }
      />
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-800">
          ZTB-E2E Analytics Dashboard
        </h1>
        <p className="text-gray-600 mt-1">Playwright Test Results Analytics</p>
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12} lg={8} xl={4}>
          <Card>
            <Statistic
              title="Total Tests"
              value={metrics?.totalTests || 0}
              loading={loading}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8} xl={5}>
          <Card>
            <Statistic
              title="Pass Rate"
              value={metrics?.overallPassRate || 0}
              precision={1}
              suffix="%"
              loading={loading}
              prefix={<CheckCircleOutlined style={{ color: "#52c41a" }} />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={8} xl={5}>
          <Card>
            <Statistic
              title="Fail Rate"
              value={metrics?.overallFailRate || 0}
              precision={1}
              suffix="%"
              loading={loading}
              prefix={<CloseCircleOutlined style={{ color: "#ff4d4f" }} />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={12} xl={5}>
          <Card>
            <Statistic
              title="Flaky Rate"
              value={metrics?.overallFlakyRate || 0}
              precision={1}
              suffix="%"
              loading={loading}
              prefix={<CheckCircleOutlined style={{ color: "#facc15" }} />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={12} xl={5}>
          <Card>
            <Statistic
              title="Skip Rate"
              value={metrics?.overallSkipRate || 0}
              precision={1}
              suffix="%"
              loading={loading}
              prefix={<CheckCircleOutlined style={{ color: "#9ca3af" }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* Slack Notification Section */}
      <div className="mt-6">
        <Card
          title={
            <span className="text-white font-semibold">
              Slack Notifications
            </span>
          }
          className="shadow-lg border-0 slack-notification-card"
          style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            borderRadius: "8px",
          }}
        >
          <div className="space-y-4 bg-white p-4 rounded-b-lg -m-6 mt-0">
            <p className="text-gray-600 mb-4">
              📢 Send comprehensive test reports to your Slack channel with
              detailed metrics, insights, and actionable recommendations.
            </p>

            <div className="flex flex-wrap gap-3 items-center">
              <SlackNotification />

              <div className="text-sm text-gray-500">
                <span className="inline-flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Channel: #ztb-e2e-report-alerts (C091V2CF5DF)
                </span>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-100">
              <h4 className="text-sm font-semibold text-gray-700 mb-2">
                🎯 What's included in the report:
              </h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li> 📊 Complete test metrics (pass/fail/flaky rates)</li>
                <li> 💡 Intelligent insights and recommendations</li>
                <li> 🎨 Beautiful formatting with status indicators</li>
                <li> ⏰ Customizable time range selection</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>

      <div className="mt-6">
        <Card title="API Status">
          <p className="text-green-600">✅ Backend is connected and working!</p>
          <p className="text-blue-600">
            📊 Data loaded successfully from Playwright test-result jsons
          </p>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={loadData}
            loading={loading}
            className="mt-4"
          >
            Refresh Data
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default SimpleDashboard;
