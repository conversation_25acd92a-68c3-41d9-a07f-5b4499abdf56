import React, { useState, useEffect } from "react";
import { Table, Card, Input, Tag, Button, Modal, Tabs, Typography } from "antd";
import {
  SearchOutlined,
  FileTextOutlined,
  FileExcelOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  TagOutlined,
  DownOutlined,
  RightOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import { useSearchParams } from "react-router-dom";
import toast from "react-hot-toast";

// Components
import FilterPanel from "@/components/filters/FilterPanel";
import AttachmentsViewer from "@/components/common/AttachmentsViewer";

// Services & Types
import apiService from "@/services/api";
import { TestRun, FilterCriteria, AttachmentInfo } from "@/types";
import { ColumnType } from "antd/es/table";

// Interface for nightly run data within a test case
interface NightlyRunData {
  id: string;
  fileName: string;
  date: string;
  time: string;
  status: string;
  duration: number;
  retryCount: number;
  allAttempts: TestRun[];
  attachments: AttachmentInfo[];
  startTime: string;
  endTime: string;
}

// Interface for grouped test cases (by test name, not by nightly run)
interface GroupedTestCase {
  id: string;
  testName: string;
  filePath: string;
  projectName: string;
  tags: string[];
  expectedStatus: string;
  nightlyRuns: NightlyRunData[]; // All nightly runs for this test case
  latestStatus: string; // Status from most recent run
  totalRuns: number; // Total number of nightly runs
  isExpanded?: boolean;
}

// Utils
import { exportFilteredTestRuns } from "@/utils/excelExport";

const { Search } = Input;
const { TabPane } = Tabs;
const { Text, Paragraph } = Typography;

const TestCases: React.FC = () => {
  const [testRuns, setTestRuns] = useState<TestRun[]>([]);
  const [allTestRuns, setAllTestRuns] = useState<TestRun[]>([]); // Store all test runs for search
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState<FilterCriteria>({});
  const [selectedRun, setSelectedRun] = useState<TestRun | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [searchParams] = useSearchParams();

  const [expandedGroups, setExpandedGroups] = useState<string[]>([]);

  useEffect(() => {
    // Parse URL parameters for initial filters
    const initialFilters: FilterCriteria = {};
    const status = searchParams.get("status");
    if (status) {
      initialFilters.status = [status];
    }
    setFilters(initialFilters);
  }, [searchParams]);

  useEffect(() => {
    loadTestRuns();
  }, [filters]);

  const loadTestRuns = async (showSuccessToast: boolean = false) => {
    try {
      setLoading(true);

      // For status filtering, we need to load all data and filter after grouping
      // because flaky status is computed at the nightly run level
      const filtersWithoutStatus = { ...filters };
      delete filtersWithoutStatus.status;

      // Load filtered data for display (without status filter)
      const filteredData = await apiService.getTestRuns(filtersWithoutStatus);
      setTestRuns(filteredData || []);

      // Load all data for search (without filters)
      const allData = await apiService.getTestRuns({});
      setAllTestRuns(allData || []);

      if (showSuccessToast) {
        toast.success("Test runs loaded successfully");
      }
    } catch (error) {
      console.error("Error loading test runs:", error);
      toast.error("Failed to load test runs");
      // Set empty arrays on error to prevent null reference errors
      setTestRuns([]);
      setAllTestRuns([]);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (newFilters: FilterCriteria) => {
    setFilters(newFilters);
  };

  const handleRefresh = () => {
    loadTestRuns(true);
  };

  const handlePaginationChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const handleSearchChange = (value: string) => {
    setSearchText(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleExport = async () => {
    try {
      await exportFilteredTestRuns(filteredTestRuns, filters);
      toast.success("Test runs exported successfully");
    } catch (error) {
      toast.error("Failed to export test runs");
    }
  };

  const handleViewTestCaseDetails = (
    testCase: GroupedTestCase,
    nightlyRun?: NightlyRunData
  ) => {
    // If a specific nightly run is provided, use its final attempt
    // Otherwise, use the most recent nightly run's final attempt
    const targetRun = nightlyRun || testCase.nightlyRuns[0]; // Most recent is first
    const finalAttempt =
      targetRun.allAttempts[targetRun.allAttempts.length - 1];

    // Add grouped information to the run for modal display
    const runWithGroupInfo = {
      ...finalAttempt,
      allAttempts: targetRun.allAttempts,
      combinedAttachments: targetRun.attachments,
      retryCount: targetRun.retryCount,
      totalDuration: targetRun.duration,
      // Use combined attachments for the main attachments tab
      allAttachmentS3Keys: targetRun.attachments,
    };
    setSelectedRun(runWithGroupInfo);
    setDetailModalVisible(true);
  };

  // Check if any individual filters are active (excluding search and date range)
  const hasIndividualFilters = React.useMemo(() => {
    return !!(
      (filters.status && filters.status.length > 0) ||
      (filters.projectName && filters.projectName.length > 0) ||
      (filters.tags && filters.tags.length > 0)
    );
  }, [filters]);

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups((prev) =>
      prev.includes(groupId)
        ? prev.filter((id) => id !== groupId)
        : [...prev, groupId]
    );
  };

  const formatDuration = (duration: number) => {
    if (duration < 1000) return `${duration}ms`;
    if (duration < 60000) return `${(duration / 1000).toFixed(1)}s`;
    return `${(duration / 60000).toFixed(1)}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "passed":
      case "expected":
        return "green";
      case "failed":
      case "unexpected":
      case "timeout":
      case "timedout":
        return "red";
      case "flaky":
        return "orange";
      case "skipped":
        return "default";
      default:
        return "blue";
    }
  };

  // Group test runs by testName + filePath + projectName (unique test cases)
  const groupTestCases = (testRuns: TestRun[]): GroupedTestCase[] => {
    const testCaseGroups: { [key: string]: TestRun[] } = {};

    // Group by unique test identifier (excluding fileName to group across nightly runs)
    testRuns.forEach((run) => {
      const testCaseKey = `${run.testName}|${run.filePath}|${run.projectName}`;
      if (!testCaseGroups[testCaseKey]) {
        testCaseGroups[testCaseKey] = [];
      }
      testCaseGroups[testCaseKey].push(run);
    });

    // Convert groups to GroupedTestCase objects
    return Object.values(testCaseGroups).map((allRuns) => {
      const firstRun = allRuns[0];

      // Group runs by nightly run (fileName)
      const nightlyRunGroups: { [fileName: string]: TestRun[] } = {};
      allRuns.forEach((run) => {
        if (!nightlyRunGroups[run.fileName]) {
          nightlyRunGroups[run.fileName] = [];
        }
        nightlyRunGroups[run.fileName].push(run);
      });

      // Create NightlyRunData for each nightly run
      const nightlyRuns: NightlyRunData[] = Object.entries(nightlyRunGroups)
        .map(([fileName, runs]) => {
          // Sort attempts by retry count (0, 1, 2, ...)
          runs.sort((a, b) => a.retry - b.retry);

          const firstAttempt = runs[0];
          const lastAttempt = runs[runs.length - 1];

          // Determine final status for this nightly run
          let finalStatus = firstAttempt.status;
          if (finalStatus === "flaky") {
            // Keep it as flaky
          } else {
            // Check if test became flaky through retries
            const hasRetries = runs.length > 1;
            const finallyPassed =
              lastAttempt.status === "passed" ||
              lastAttempt.status === "expected";
            if (hasRetries && finallyPassed) {
              finalStatus = "flaky";
            } else {
              finalStatus = lastAttempt.status;
            }
          }

          // Combine all attachments from all attempts in this nightly run
          const attachments: AttachmentInfo[] = [];
          runs.forEach((attempt, index) => {
            if (
              attempt.allAttachmentS3Keys &&
              attempt.allAttachmentS3Keys.length > 0
            ) {
              attempt.allAttachmentS3Keys.forEach((attachment) => {
                const uniqueAttachment = {
                  ...attachment,
                  name:
                    index === 0
                      ? attachment.name
                      : `${attachment.name} (Retry ${index})`,
                  fileName: attachment.fileName,
                  attemptIndex: index,
                  retryNumber: attempt.retry,
                  s3Key: attachment.s3Key,
                };
                attachments.push(uniqueAttachment);
              });
            }
          });

          // Extract date and time from fileName
          const match = fileName.match(
            /(\d{4}-\d{2}-\d{2})T(\d{2}:\d{2}:\d{2})/
          );
          const date = match ? match[1] : fileName;
          const time = match ? match[2] : "";

          return {
            id: `nightly-${fileName}-${firstAttempt.testName}`,
            fileName,
            date,
            time,
            status: finalStatus,
            duration: runs.reduce((sum, attempt) => sum + attempt.duration, 0),
            retryCount: runs.length - 1,
            allAttempts: runs,
            attachments,
            startTime: firstAttempt.startTime,
            endTime: lastAttempt.startTime,
          };
        })
        .sort((a, b) => b.date.localeCompare(a.date)); // Sort by date descending (most recent first)

      // Get latest status from most recent nightly run
      const latestStatus = nightlyRuns[0]?.status || "unknown";

      return {
        id: `testcase-${firstRun.testName}-${firstRun.filePath}-${firstRun.projectName}`,
        testName: firstRun.testName,
        filePath: firstRun.filePath,
        projectName: firstRun.projectName,
        tags: firstRun.tags,
        expectedStatus: firstRun.expectedStatus,
        nightlyRuns,
        latestStatus,
        totalRuns: nightlyRuns.length,
        isExpanded: false,
      };
    });
  };

  // Column definitions for grouped test cases
  const testCaseColumns: ColumnType<GroupedTestCase>[] = [
    {
      title: "S.No",
      key: "serialNumber",
      width: 80,
      render: (_: any, __: any, index: number) =>
        (currentPage - 1) * pageSize + index + 1,
    },
    {
      title: "Test Name",
      key: "testName",
      width: 350,
      render: (_, record: GroupedTestCase) => {
        const isExpanded = expandedGroups.includes(record.id);
        return (
          <div
            className={
              hasIndividualFilters
                ? "bg-red-50 border border-red-200 rounded-md p-2"
                : ""
            }
          >
            <div className="flex items-center space-x-2">
              {record.totalRuns > 1 && (
                <Button
                  type="text"
                  size="small"
                  icon={isExpanded ? <DownOutlined /> : <RightOutlined />}
                  onClick={() => toggleGroupExpansion(record.id)}
                  className={`p-0 h-auto ${
                    hasIndividualFilters ? "text-red-600" : ""
                  }`}
                />
              )}
              <div className="flex-1">
                <div
                  className={`font-medium truncate ${
                    hasIndividualFilters
                      ? "text-red-700 font-semibold"
                      : "text-gray-900"
                  }`}
                  title={record.testName}
                >
                  {record.testName}
                </div>
                <div className="flex items-center space-x-1 mt-1">
                  <FileTextOutlined
                    className={`text-xs ${
                      hasIndividualFilters ? "text-red-400" : "text-gray-400"
                    }`}
                  />
                  <span
                    className={`text-xs truncate ${
                      hasIndividualFilters ? "text-red-600" : "text-gray-500"
                    }`}
                    title={record.filePath}
                  >
                    {record.filePath}
                  </span>
                </div>
              </div>
            </div>

            {/* Expanded nightly runs */}
            {isExpanded && record.totalRuns > 1 && (
              <div className="mt-3 ml-6 border-l-2 border-gray-200 pl-4">
                {record.nightlyRuns.map((nightlyRun) => {
                  // Determine display status and color
                  let displayStatus = nightlyRun.status;
                  let statusColor = "#6b7280"; // gray-700

                  if (
                    nightlyRun.status === "passed" ||
                    nightlyRun.status === "expected"
                  ) {
                    displayStatus = "Passed";
                    statusColor = "#16a34a"; // green-600
                  } else if (
                    nightlyRun.status === "failed" ||
                    nightlyRun.status === "unexpected" ||
                    nightlyRun.status === "timeout"
                  ) {
                    displayStatus = "Failed";
                    statusColor = "#dc2626"; // red-600
                  } else if (nightlyRun.status === "flaky") {
                    displayStatus = "Flaky";
                    statusColor = "#ea580c"; // orange-600
                  } else if (nightlyRun.status === "skipped") {
                    displayStatus = "Skipped";
                    statusColor = "#6b7280"; // gray-600
                  } else {
                    displayStatus =
                      nightlyRun.status.charAt(0).toUpperCase() +
                      nightlyRun.status.slice(1);
                    statusColor = "#2563eb"; // blue-600
                  }

                  return (
                    <div
                      key={nightlyRun.id}
                      className={`py-2 text-sm border-b border-gray-100 last:border-b-0 ${
                        hasIndividualFilters
                          ? "bg-red-50 border-red-100 rounded-md px-3 mb-2"
                          : ""
                      }`}
                    >
                      {hasIndividualFilters ? (
                        // Enhanced format when filters are active - similar to your example
                        <div className="flex items-center space-x-1">
                          <span className="font-medium text-red-700 text-sm">
                            {nightlyRun.date}
                          </span>
                          <span className="font-medium text-red-700 text-sm">
                            {nightlyRun.time}
                          </span>
                          <Tag
                            color={getStatusColor(nightlyRun.status)}
                            className="font-medium mx-1"
                          >
                            {displayStatus}
                          </Tag>
                          {nightlyRun.retryCount > 0 && (
                            <Tag
                              color="orange"
                              className="text-xs font-medium mx-1"
                            >
                              {nightlyRun.retryCount} retry
                            </Tag>
                          )}
                          <span className="text-red-600 font-medium text-sm mx-1">
                            {formatDuration(nightlyRun.duration)}
                          </span>
                          <EyeOutlined className="text-red-600 mx-1" />
                          <Button
                            type="link"
                            size="small"
                            onClick={() =>
                              handleViewTestCaseDetails(record, nightlyRun)
                            }
                            className="p-0 text-red-600 hover:text-red-700 font-medium underline"
                            style={{
                              color: "#dc2626",
                              fontWeight: "500",
                              textDecoration: "underline",
                            }}
                          >
                            View
                          </Button>
                        </div>
                      ) : (
                        // Original format when no individual filters
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex flex-col">
                              <div className="font-medium text-gray-800">
                                {nightlyRun.date}
                              </div>
                              <div className="text-xs text-gray-500">
                                {nightlyRun.time}
                              </div>
                            </div>
                            <span
                              className="font-medium"
                              style={{ color: statusColor }}
                            >
                              {displayStatus}
                            </span>
                            {nightlyRun.retryCount > 0 && (
                              <Tag color="orange" className="text-xs">
                                {nightlyRun.retryCount}{" "}
                                {nightlyRun.retryCount === 1
                                  ? "retry"
                                  : "retries"}
                              </Tag>
                            )}
                          </div>
                          <div className="flex items-center space-x-3">
                            <span className="text-gray-600">
                              {formatDuration(nightlyRun.duration)}
                            </span>
                            <Button
                              type="link"
                              size="small"
                              icon={<EyeOutlined />}
                              onClick={() =>
                                handleViewTestCaseDetails(record, nightlyRun)
                              }
                              className="p-0"
                            >
                              View
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        );
      },
      sorter: (a: GroupedTestCase, b: GroupedTestCase) =>
        a.testName.localeCompare(b.testName),
    },
    {
      title: "Latest Status",
      key: "latestStatus",
      width: 180,
      render: (_, record: GroupedTestCase) => {
        let displayStatus = record.latestStatus;
        if (record.latestStatus === "expected") {
          displayStatus = "Passed";
        } else if (
          record.latestStatus === "timeout" ||
          record.latestStatus === "timedOut" ||
          record.latestStatus === "unexpected"
        ) {
          displayStatus = "Failed";
        } else {
          displayStatus =
            record.latestStatus.charAt(0).toUpperCase() +
            record.latestStatus.slice(1);
        }

        const latestRun = record.nightlyRuns[0]; // Most recent run
        const retryText =
          latestRun.retryCount > 0 ? ` ${latestRun.retryCount} retry` : "";

        return (
          <div
            className={`flex items-center space-x-2 ${
              hasIndividualFilters ? "bg-red-50 p-2 rounded-md" : ""
            }`}
          >
            <Tag
              color={getStatusColor(record.latestStatus)}
              className={hasIndividualFilters ? "font-medium" : ""}
            >
              {displayStatus}
            </Tag>
            <span
              className={`text-xs ${
                hasIndividualFilters
                  ? "text-red-600 font-medium"
                  : "text-gray-600"
              }`}
            >
              {retryText}
            </span>
          </div>
        );
      },
      sorter: (a: GroupedTestCase, b: GroupedTestCase) =>
        a.latestStatus.localeCompare(b.latestStatus),
    },
    {
      title: "Latest Run Time",
      key: "latestRunTime",
      width: 150,
      render: (_, record: GroupedTestCase) => {
        const latestRun = record.nightlyRuns[0]; // Most recent run
        return (
          <div
            className={`text-xs ${
              hasIndividualFilters ? "bg-red-50 p-2 rounded-md" : ""
            }`}
          >
            <div
              className={`font-medium ${
                hasIndividualFilters ? "text-red-700" : ""
              }`}
            >
              {latestRun.date}
            </div>
            <div
              className={
                hasIndividualFilters ? "text-red-600" : "text-gray-500"
              }
            >
              {latestRun.time}
            </div>
          </div>
        );
      },
      sorter: (a: GroupedTestCase, b: GroupedTestCase) =>
        b.nightlyRuns[0].date.localeCompare(a.nightlyRuns[0].date),
    },
    {
      title: "Total Runs",
      key: "totalRuns",
      width: 100,
      render: (_, record: GroupedTestCase) => (
        <div
          className={`text-center ${
            hasIndividualFilters ? "bg-red-50 p-2 rounded-md" : ""
          }`}
        >
          <span
            className={`font-medium ${
              hasIndividualFilters ? "text-red-600" : "text-blue-600"
            }`}
          >
            {record.totalRuns}
          </span>
          <div
            className={`text-xs ${
              hasIndividualFilters ? "text-red-600" : "text-gray-500"
            }`}
          >
            {record.totalRuns === 1 ? "run" : "runs"}
          </div>
        </div>
      ),
      sorter: (a: GroupedTestCase, b: GroupedTestCase) =>
        a.totalRuns - b.totalRuns,
    },
    {
      title: "Project",
      key: "projectName",
      width: 120,
      render: (_, record: GroupedTestCase) => (
        <div className={hasIndividualFilters ? "bg-red-50 p-2 rounded-md" : ""}>
          <Tag
            color={hasIndividualFilters ? "red" : "blue"}
            className={hasIndividualFilters ? "font-medium" : ""}
          >
            {record.projectName}
          </Tag>
        </div>
      ),
      sorter: (a: GroupedTestCase, b: GroupedTestCase) =>
        a.projectName.localeCompare(b.projectName),
    },
  ];

  // Filter and group test cases based on search text and selected date
  const filteredGroupedTestCases = React.useMemo(() => {
    let result;

    // Start with the base data (filtered by other criteria from API)
    let baseData = testRuns || [];

    // If a specific date is selected, filter by that date
    if (filters.selectedDate) {
      baseData = baseData.filter((run) => {
        const dateMatch = run.fileName?.match(/(\d{4}-\d{2}-\d{2})/);
        const runDate = dateMatch ? dateMatch[1] : null;
        return runDate === filters.selectedDate;
      });
    }

    // If no search text, use the date-filtered data
    if (!searchText.trim()) {
      result = baseData;
    } else {
      // If there's search text, search in ALL test runs but apply all filters
      const searchLower = searchText.toLowerCase().trim();
      let searchData = allTestRuns || [];

      // Apply date filter to search data if selected
      if (filters.selectedDate) {
        searchData = searchData.filter((run) => {
          const dateMatch = run.fileName?.match(/(\d{4}-\d{2}-\d{2})/);
          const runDate = dateMatch ? dateMatch[1] : null;
          return runDate === filters.selectedDate;
        });
      }

      // Apply project filter to search data if selected
      if (filters.projectName && filters.projectName.length > 0) {
        searchData = searchData.filter((run) => {
          return filters.projectName!.some(
            (project) => run.projectName.toLowerCase() === project.toLowerCase()
          );
        });
      }

      // Apply status filter to search data if selected
      if (filters.status && filters.status.length > 0) {
        searchData = searchData.filter((run) => {
          return filters.status!.includes(run.status);
        });
      }

      result = searchData.filter((run) => {
        return (
          run.testName.toLowerCase().includes(searchLower) ||
          run.filePath.toLowerCase().includes(searchLower)
        );
      });
    }

    // Group the filtered results
    return groupTestCases(result || []);
  }, [
    testRuns,
    allTestRuns,
    searchText,
    filters.selectedDate,
    filters.projectName,
    filters.status,
  ]);

  // For backward compatibility with export and other features that expect individual runs
  const filteredTestRuns = React.useMemo(() => {
    return filteredGroupedTestCases.flatMap((testCase) =>
      testCase.nightlyRuns.flatMap((nightlyRun) => nightlyRun.allAttempts)
    );
  }, [filteredGroupedTestCases]);

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Filters */}
      <FilterPanel
        key={JSON.stringify(filters)}
        onFilterChange={handleFilterChange}
        onRefresh={handleRefresh}
        refreshLoading={loading}
        initialFilters={filters}
      />

      {/* Test Runs Table */}
      <Card className="shadow-sm">
        <div className="mb-4" style={{ width: "100%" }}>
          <div
            style={{
              display: "flex",
              alignItems: "center",
              width: "100%",
              justifyContent: "space-between",
            }}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "16px" }}>
              <Search
                placeholder="Search by test name or file path..."
                allowClear
                enterButton={<SearchOutlined />}
                size="large"
                value={searchText}
                onChange={(e) => handleSearchChange(e.target.value)}
                onSearch={handleSearchChange}
                style={{ width: 400 }}
              />
              <span className="text-gray-500" style={{ whiteSpace: "nowrap" }}>
                Showing {filteredGroupedTestCases.length} test cases (
                {filteredTestRuns.length} total runs)
              </span>
            </div>
            <Button
              type="primary"
              icon={<FileExcelOutlined />}
              onClick={handleExport}
              disabled={filteredTestRuns.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
              style={{ flexShrink: 0 }}
            >
              Export to Excel
            </Button>
          </div>
        </div>

        {/* If searching, show grouped table */}
        {searchText.trim() ? (
          <Table
            columns={testCaseColumns}
            dataSource={filteredGroupedTestCases}
            loading={loading}
            rowKey={(record) => record.id}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ["10", "20", "50", "100", "200"],
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} test cases`,
              onChange: handlePaginationChange,
              onShowSizeChange: handlePaginationChange,
              size: "default",
            }}
            scroll={{ x: 1520 }}
            size="small"
            onRow={(record) => ({
              onDoubleClick: () => handleViewTestCaseDetails(record),
              style: {
                cursor: "pointer",
                ...(hasIndividualFilters
                  ? {
                      backgroundColor: "#fef2f2",
                      borderLeft: "3px solid #dc2626",
                    }
                  : {}),
              },
            })}
            locale={{
              emptyText: (
                <div className="py-8">
                  <div className="text-center">
                    <FileTextOutlined className="text-4xl text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No test runs found
                    </h3>
                    <p className="text-gray-500 mb-4">
                      No test runs match your search "{searchText}"
                    </p>
                    <Button
                      type="primary"
                      onClick={() => {
                        setSearchText("");
                      }}
                    >
                      Clear search
                    </Button>
                  </div>
                </div>
              ),
            }}
          />
        ) : (
          // Show filtered table based on selected date
          <Table
            columns={testCaseColumns}
            dataSource={filteredGroupedTestCases}
            loading={loading}
            rowKey={(record) => record.id}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              showSizeChanger: true,
              showQuickJumper: true,
              pageSizeOptions: ["10", "20", "50", "100", "200"],
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} test cases`,
              onChange: handlePaginationChange,
              onShowSizeChange: handlePaginationChange,
              size: "default",
            }}
            scroll={{ x: 1520 }}
            size="small"
            onRow={(record) => ({
              onDoubleClick: () => handleViewTestCaseDetails(record),
              style: {
                cursor: "pointer",
                ...(hasIndividualFilters
                  ? {
                      backgroundColor: "#fef2f2",
                      borderLeft: "3px solid #dc2626",
                    }
                  : {}),
              },
            })}
            locale={{
              emptyText: (
                <div className="py-8">
                  <div className="text-center">
                    <FileTextOutlined className="text-4xl text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No test runs found
                    </h3>
                    <p className="text-gray-500 mb-4">
                      {filters.selectedDate
                        ? `No test runs found for ${filters.selectedDate}`
                        : "No test runs match your current filters"}
                    </p>
                    <Button
                      type="primary"
                      onClick={() => {
                        setFilters({});
                        setSearchText("");
                      }}
                    >
                      Clear filters
                    </Button>
                  </div>
                </div>
              ),
            }}
          />
        )}
      </Card>

      {/* Test Run Detail Modal */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <EyeOutlined />
            <span>Test Run Details</span>
            {selectedRun && (
              <Tag color={getStatusColor(selectedRun.status)}>
                {selectedRun.status === "expected"
                  ? "Passed"
                  : selectedRun.status === "timeout" ||
                    selectedRun.status === "timedOut" ||
                    selectedRun.status === "unexpected"
                  ? "Failed"
                  : selectedRun.status.charAt(0).toUpperCase() +
                    selectedRun.status.slice(1)}
              </Tag>
            )}
          </div>
        }
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        width={1200}
        footer={null}
        className="test-run-detail-modal"
      >
        {selectedRun && (
          <div className="space-y-4">
            {/* Test Information */}
            <Card size="small" title="Test Information">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text strong>Test Name:</Text>
                  <Paragraph copyable className="mb-2">
                    {selectedRun.testName}
                  </Paragraph>
                </div>
                <div>
                  <Text strong>File Path:</Text>
                  <Paragraph copyable className="mb-2">
                    {selectedRun.filePath}
                  </Paragraph>
                </div>
                <div>
                  <Text strong>Project:</Text>
                  <div>
                    <Tag color="blue">{selectedRun.projectName}</Tag>
                  </div>
                </div>
                <div>
                  <Text strong>Duration:</Text>
                  <div className="flex items-center space-x-2">
                    <ClockCircleOutlined />
                    <span>
                      {formatDuration(selectedRun.duration)}
                      {(selectedRun as any).totalDuration &&
                        (selectedRun as any).totalDuration !==
                          selectedRun.duration && (
                          <span className="text-gray-500 ml-2">
                            (Total:{" "}
                            {formatDuration((selectedRun as any).totalDuration)}
                            )
                          </span>
                        )}
                    </span>
                  </div>
                </div>
                <div>
                  <Text strong>Start Time:</Text>
                  <div>{new Date(selectedRun.startTime).toLocaleString()}</div>
                </div>
                <div>
                  <Text strong>Retry Count:</Text>
                  <div className="flex items-center space-x-2">
                    <span>{selectedRun.retry}</span>
                    {(selectedRun as any).retryCount !== undefined &&
                      (selectedRun as any).retryCount > 0 && (
                        <Tag color="orange" className="text-xs">
                          {(selectedRun as any).retryCount} total{" "}
                          {(selectedRun as any).retryCount === 1
                            ? "retry"
                            : "retries"}
                        </Tag>
                      )}
                  </div>
                </div>
                <div>
                  <Text strong>Expected Status:</Text>
                  <div>
                    <Tag>{selectedRun.expectedStatus}</Tag>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {selectedRun.tags.length > 0 && (
                <div className="mt-4">
                  <Text strong>Tags:</Text>
                  <div className="mt-2">
                    {selectedRun.tags.map((tag, index) => (
                      <Tag key={index} icon={<TagOutlined />} color="purple">
                        {tag}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

              {/* Annotations */}
              {selectedRun.annotations.length > 0 && (
                <div className="mt-4">
                  <Text strong>Annotations:</Text>
                  <div className="mt-2 space-y-1">
                    {selectedRun.annotations.map((annotation, index) => (
                      <div key={index} className="text-sm">
                        <Text code>{annotation.type}</Text>:{" "}
                        {annotation.description}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </Card>

            {/* Tabs for detailed information */}
            <Tabs defaultActiveKey="steps" type="card">
              {/* Steps Tab */}
              <TabPane tab={`Steps (${selectedRun.steps.length})`} key="steps">
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {selectedRun.steps.map((step, index) => (
                    <Card
                      key={index}
                      size="small"
                      className="border-l-4 border-l-blue-500"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <Text strong>{step.title}</Text>
                          {step.error && (
                            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                              <Text type="danger" className="text-sm">
                                {step.error.message}
                              </Text>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <ClockCircleOutlined />
                          <span>{formatDuration(step.duration)}</span>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </TabPane>

              {/* Retry Timeline Tab - only show if there are multiple attempts */}
              {(selectedRun as any).allAttempts &&
                (selectedRun as any).allAttempts.length > 1 && (
                  <TabPane
                    tab={`Retry Timeline (${
                      (selectedRun as any).allAttempts.length
                    })`}
                    key="retryTimeline"
                  >
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {(selectedRun as any).allAttempts.map(
                        (attempt: TestRun, index: number) => {
                          // Determine if this is a flaky test
                          const isFlaky =
                            (selectedRun as any).allAttempts.length > 1 &&
                            (selectedRun as any).allAttempts.some(
                              (a: TestRun) =>
                                a.status === "passed" || a.status === "expected"
                            );

                          // Determine display status and colors for flaky tests
                          let displayStatus = attempt.status;
                          let borderColor = "border-l-blue-500";
                          let dotColor = "bg-blue-500";
                          let tagColor = getStatusColor(attempt.status);

                          if (isFlaky) {
                            if (index === 0) {
                              // Original attempt in flaky test should be "Failed" (red)
                              displayStatus = "Failed";
                              borderColor = "border-l-red-500";
                              dotColor = "bg-red-500";
                              tagColor = "red";
                            } else if (
                              attempt.status === "passed" ||
                              attempt.status === "expected"
                            ) {
                              // Retry that passed should be "Passed" (green)
                              displayStatus = "Passed";
                              borderColor = "border-l-green-500";
                              dotColor = "bg-green-500";
                              tagColor = "green";
                            } else {
                              // Retry that failed should be "Failed" (red)
                              displayStatus = "Failed";
                              borderColor = "border-l-red-500";
                              dotColor = "bg-red-500";
                              tagColor = "red";
                            }
                          } else {
                            // For non-flaky tests, use standard logic
                            if (
                              attempt.status === "passed" ||
                              attempt.status === "expected"
                            ) {
                              displayStatus = "Passed";
                              borderColor = "border-l-green-500";
                              dotColor = "bg-green-500";
                              tagColor = "green";
                            } else if (
                              attempt.status === "failed" ||
                              attempt.status === "unexpected" ||
                              attempt.status === "timeout"
                            ) {
                              displayStatus = "Failed";
                              borderColor = "border-l-red-500";
                              dotColor = "bg-red-500";
                              tagColor = "red";
                            } else if (attempt.status === "skipped") {
                              displayStatus = "Skipped";
                              borderColor = "border-l-gray-400";
                              dotColor = "bg-gray-400";
                              tagColor = "default";
                            } else {
                              displayStatus =
                                attempt.status.charAt(0).toUpperCase() +
                                attempt.status.slice(1);
                              borderColor = "border-l-blue-500";
                              dotColor = "bg-blue-500";
                              tagColor = "blue";
                            }
                          }

                          return (
                            <Card
                              key={attempt.id}
                              size="small"
                              className={`border-l-4 ${borderColor}`}
                            >
                              <div className="space-y-3">
                                {/* Attempt Header */}
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <div className="flex items-center space-x-2">
                                      <span
                                        className={`w-3 h-3 rounded-full ${dotColor}`}
                                      />
                                      <Text strong className="text-lg">
                                        {index === 0
                                          ? "Original"
                                          : `Retry ${index}`}
                                      </Text>
                                    </div>
                                    <Tag color={tagColor}>{displayStatus}</Tag>
                                  </div>
                                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                                    <div className="flex items-center space-x-1">
                                      <ClockCircleOutlined />
                                      <span>
                                        {formatDuration(attempt.duration)}
                                      </span>
                                    </div>
                                    <span>
                                      {new Date(
                                        attempt.startTime
                                      ).toLocaleTimeString()}
                                    </span>
                                  </div>
                                </div>

                                {/* Attempt Details */}
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                  <div>
                                    <Text strong>Worker Index:</Text>{" "}
                                    {attempt.workerIndex}
                                  </div>
                                  <div>
                                    <Text strong>Parallel Index:</Text>{" "}
                                    {attempt.parallelIndex}
                                  </div>
                                </div>

                                {/* Errors for this attempt */}
                                {attempt.errors.length > 0 && (
                                  <div>
                                    <Text
                                      strong
                                      type="danger"
                                      className="text-sm"
                                    >
                                      Errors ({attempt.errors.length}):
                                    </Text>
                                    <div className="mt-2 space-y-2">
                                      {attempt.errors.map(
                                        (error, errorIndex) => (
                                          <div
                                            key={errorIndex}
                                            className="p-2 bg-red-50 border border-red-200 rounded text-sm"
                                          >
                                            <Text type="danger">
                                              {error.message}
                                            </Text>
                                            {error.location && (
                                              <div className="text-xs text-gray-600 mt-1">
                                                {error.location.file}:
                                                {error.location.line}:
                                                {error.location.column}
                                              </div>
                                            )}
                                          </div>
                                        )
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Attachments for this attempt */}
                                {attempt.allAttachmentS3Keys &&
                                  attempt.allAttachmentS3Keys.length > 0 && (
                                    <div>
                                      <Text strong className="text-sm">
                                        Attachments (
                                        {attempt.allAttachmentS3Keys.length}):
                                      </Text>
                                      <div className="mt-2 flex flex-wrap gap-2">
                                        {attempt.allAttachmentS3Keys.map(
                                          (attachment, attIndex) => (
                                            <Tag
                                              key={attIndex}
                                              color="blue"
                                              className="text-xs"
                                            >
                                              {attachment.type}:{" "}
                                              {attachment.fileName}
                                            </Tag>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            </Card>
                          );
                        }
                      )}
                    </div>
                  </TabPane>
                )}

              {/* Errors Tab */}
              {selectedRun.errors.length > 0 && (
                <TabPane
                  tab={`Errors (${selectedRun.errors.length})`}
                  key="errors"
                >
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {selectedRun.errors.map((error, index) => (
                      <Card
                        key={index}
                        size="small"
                        className="border-l-4 border-l-red-500"
                      >
                        <div>
                          <Text strong type="danger">
                            Error {index + 1}:
                          </Text>
                          <Paragraph className="mt-2 mb-2">
                            {error.message}
                          </Paragraph>
                          {error.location && (
                            <div className="text-sm text-gray-600 mb-2">
                              Location: {error.location.file}:
                              {error.location.line}:{error.location.column}
                            </div>
                          )}
                          {error.stack && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-sm text-blue-600">
                                Show Stack Trace
                              </summary>
                              <pre className="mt-2 p-2 bg-gray-100 text-xs overflow-x-auto">
                                {error.stack}
                              </pre>
                            </details>
                          )}
                          {error.snippet && (
                            <details className="mt-2">
                              <summary className="cursor-pointer text-sm text-blue-600">
                                Show Code Snippet
                              </summary>
                              <pre className="mt-2 p-2 bg-gray-100 text-xs overflow-x-auto">
                                {error.snippet}
                              </pre>
                            </details>
                          )}
                        </div>
                      </Card>
                    ))}
                  </div>
                </TabPane>
              )}

              {/* Stdout Tab */}
              {selectedRun.stdout.length > 0 && (
                <TabPane
                  tab={`Output (${selectedRun.stdout.length})`}
                  key="stdout"
                >
                  <div className="max-h-96 overflow-y-auto">
                    <pre className="text-xs bg-gray-100 p-4 rounded">
                      {selectedRun.stdout.map((output, index) => (
                        <div key={index}>{output.text}</div>
                      ))}
                    </pre>
                  </div>
                </TabPane>
              )}

              {/* Stderr Tab */}
              {selectedRun.stderr.length > 0 && (
                <TabPane
                  tab={`Errors Output (${selectedRun.stderr.length})`}
                  key="stderr"
                >
                  <div className="max-h-96 overflow-y-auto">
                    <pre className="text-xs bg-red-50 p-4 rounded border border-red-200">
                      {selectedRun.stderr.map((output, index) => (
                        <div key={index} className="text-red-700">
                          {output.text}
                        </div>
                      ))}
                    </pre>
                  </div>
                </TabPane>
              )}

              {/* Attachments Tab */}
              {selectedRun.allAttachmentS3Keys &&
                selectedRun.allAttachmentS3Keys.length > 0 && (
                  <TabPane
                    tab={`Attachments (${selectedRun.allAttachmentS3Keys.length})`}
                    key="attachments"
                  >
                    <div className="max-h-96 overflow-y-auto">
                      <AttachmentsViewer
                        attachments={selectedRun.allAttachmentS3Keys}
                        testName={selectedRun.testName}
                        inline={true}
                      />
                    </div>
                  </TabPane>
                )}
            </Tabs>
          </div>
        )}
      </Modal>
    </motion.div>
  );
};

export default TestCases;
