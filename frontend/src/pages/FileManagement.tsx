import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  Upload,
  Button,
  List,
  Space,
  Popconfirm,
  Progress,
  Alert,
  Input,
  Modal,
  Typography,
} from "antd";
import {
  UploadOutlined,
  DeleteOutlined,
  FileTextOutlined,
  CloudUploadOutlined,
  ReloadOutlined,
  EditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { motion } from "framer-motion";
import { useDropzone } from "react-dropzone";
import toast from "react-hot-toast";

// Services & Types
import apiService from "@/services/api";
import { FileInfo, JSONUploadRequest } from "@/types";

const { Dragger } = Upload;
const { TextArea } = Input;
const { Text } = Typography;

const FileManagement: React.FC = () => {
  const [files, setFiles] = useState<FileInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // JSON Paste functionality state
  const [jsonModalVisible, setJsonModalVisible] = useState(false);
  const [jsonContent, setJsonContent] = useState("");
  const [fileName, setFileName] = useState("");
  const [jsonValidation, setJsonValidation] = useState<{
    isValid: boolean;
    error?: string;
  } | null>(null);
  const [jsonUploading, setJsonUploading] = useState(false);

  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    try {
      setLoading(true);
      const response = await apiService.getFiles();
      setFiles(response.files);
    } catch (error) {
      toast.error("Failed to load files");
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (file: File) => {
    try {
      setUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      await apiService.uploadFile(file);

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Reload files after successful upload
      await loadFiles();

      setTimeout(() => {
        setUploadProgress(0);
      }, 1000);
    } catch (error) {
      toast.error("Failed to upload file");
    } finally {
      setUploading(false);
    }
  };

  const handleMultipleFileUpload = async (files: File[]) => {
    try {
      setUploading(true);
      setUploadProgress(0);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 300);

      await apiService.uploadMultipleFiles(files);

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Reload files after successful upload
      await loadFiles();

      setTimeout(() => {
        setUploadProgress(0);
      }, 1000);
    } catch (error) {
      toast.error("Failed to upload files");
    } finally {
      setUploading(false);
    }
  };

  const handleFileDelete = async (fileName: string) => {
    try {
      await apiService.deleteFile(fileName);
      await loadFiles();
    } catch (error) {
      toast.error("Failed to delete file");
    }
  };

  // JSON Paste functionality handlers
  const validateJSON = (content: string) => {
    if (!content.trim()) {
      setJsonValidation(null);
      return;
    }

    try {
      const parsed = JSON.parse(content);

      // Basic validation for Playwright structure
      if (!parsed.config) {
        setJsonValidation({
          isValid: false,
          error: "Invalid Playwright test result format - missing config",
        });
        return;
      }

      if (!parsed.suites) {
        setJsonValidation({
          isValid: false,
          error: "Invalid Playwright test result format - missing suites",
        });
        return;
      }

      setJsonValidation({ isValid: true });
    } catch (error) {
      setJsonValidation({
        isValid: false,
        error: "Invalid JSON format",
      });
    }
  };

  const handleJSONContentChange = (value: string) => {
    setJsonContent(value);
    validateJSON(value);
  };

  const handleJSONUpload = async () => {
    if (!jsonContent.trim()) {
      toast.error("Please paste JSON content");
      return;
    }

    if (!jsonValidation?.isValid) {
      toast.error("Please fix JSON validation errors before uploading");
      return;
    }

    try {
      setJsonUploading(true);

      const request: JSONUploadRequest = {
        jsonContent: jsonContent.trim(),
        fileName: fileName.trim() || undefined,
      };

      await apiService.uploadJSONText(request);

      // Reset form and close modal
      setJsonContent("");
      setFileName("");
      setJsonValidation(null);
      setJsonModalVisible(false);

      // Reload files
      await loadFiles();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to upload JSON content");
      }
    } finally {
      setJsonUploading(false);
    }
  };

  const resetJSONForm = () => {
    setJsonContent("");
    setFileName("");
    setJsonValidation(null);
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const jsonFiles = acceptedFiles.filter((file) =>
      file.name.endsWith(".json")
    );

    if (jsonFiles.length === 0) {
      toast.error("Please upload only JSON files");
      return;
    }

    if (jsonFiles.length === 1) {
      handleFileUpload(jsonFiles[0]);
    } else {
      handleMultipleFileUpload(jsonFiles);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/json": [".json"],
    },
    multiple: true,
  });

  const uploadProps = {
    name: "file",
    multiple: true,
    accept: ".json",
    beforeUpload: (file: File) => {
      if (!file.name.endsWith(".json")) {
        toast.error("Please upload only JSON files");
        return false;
      }
      handleFileUpload(file);
      return false; // Prevent default upload
    },
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-800">File Management</h1>
          <p className="text-gray-600 mt-1">
            Upload and manage Playwright test result JSON files
          </p>
        </div>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={loadFiles}
          loading={loading}
        >
          Refresh Data
        </Button>
      </div>

      {/* Upload Section */}
      <Card title="Upload Test Results" className="mb-6">
        <div className="space-y-4">
          {/* Drag & Drop Upload */}
          <div
            {...getRootProps()}
            className={`upload-area ${isDragActive ? "dragover" : ""}`}
          >
            <input {...getInputProps()} />
            <div className="text-center">
              <CloudUploadOutlined className="text-4xl text-blue-500 mb-4" />
              {isDragActive ? (
                <p className="text-lg text-blue-600">
                  Drop the JSON files here...
                </p>
              ) : (
                <div>
                  <p className="text-lg text-gray-700 mb-2">
                    Drag & drop JSON files here, or click to select
                  </p>
                  <p className="text-sm text-gray-500">
                    Supports multiple file upload. Only .json files are
                    accepted.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Traditional Upload */}
          <div className="text-center">
            <span className="text-gray-500">or</span>
          </div>

          <Dragger {...uploadProps} className="upload-dragger">
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">
              Click or drag files to this area to upload
            </p>
            <p className="ant-upload-hint">
              Support for single or bulk upload of Playwright JSON result files.
            </p>
          </Dragger>

          {/* JSON Paste Option */}
          <div className="text-center">
            <span className="text-gray-500">or</span>
          </div>

          <div className="text-center">
            <Button
              type="dashed"
              icon={<EditOutlined />}
              size="large"
              onClick={() => setJsonModalVisible(true)}
              className="w-full h-16"
            >
              <div>
                <div className="text-lg">Paste JSON Content</div>
                <div className="text-sm text-gray-500">
                  Copy and paste Playwright test result JSON directly
                </div>
              </div>
            </Button>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              className="mt-4"
            >
              <Progress
                percent={uploadProgress}
                status={uploadProgress === 100 ? "success" : "active"}
                strokeColor={{
                  "0%": "#108ee9",
                  "100%": "#87d068",
                }}
              />
            </motion.div>
          )}
        </div>
      </Card>

      {/* Files List */}
      <Card
        title={`Uploaded Files (${files.length})`}
        extra={
          <Space>
            <span className="text-sm text-gray-500">
              Total: {files.length} files
            </span>
          </Space>
        }
      >
        {files.length === 0 ? (
          <div className="text-center py-12">
            <FileTextOutlined className="text-4xl text-gray-400 mb-4" />
            <p className="text-gray-500 text-lg">No files uploaded yet</p>
            <p className="text-gray-400 text-sm">
              Upload your first Playwright test result JSON file to get started
            </p>
          </div>
        ) : (
          <List
            dataSource={files}
            loading={loading}
            renderItem={(file, index) => (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <List.Item
                  actions={[
                    <Popconfirm
                      title="Are you sure you want to delete this file?"
                      description="This action cannot be undone."
                      onConfirm={() => handleFileDelete(file.name)}
                      okText="Yes"
                      cancelText="No"
                      okType="danger"
                    >
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        size="small"
                      >
                        Delete
                      </Button>
                    </Popconfirm>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <FileTextOutlined className="text-blue-500 text-xl" />
                    }
                    title={
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{file.name}</span>
                      </div>
                    }
                    description={
                      <div className="text-sm text-gray-500">
                        <div>Size: {file.size}</div>
                        <div>Type: {file.type}</div>
                      </div>
                    }
                  />
                </List.Item>
              </motion.div>
            )}
          />
        )}
      </Card>

      {/* Help Section */}
      <Card title="Upload Guidelines">
        <Alert
          message="File Format Requirements"
          description={
            <div className="space-y-2">
              <p>
                • Only JSON files generated by Playwright test runs are
                supported
              </p>
              <p>
                • Files should contain valid Playwright test result structure
                with 'config' and 'suites' properties
              </p>
              <p>
                • Multiple files can be uploaded simultaneously for batch
                processing
              </p>
              <p>
                • You can also paste JSON content directly using the "Paste JSON
                Content" option
              </p>
              <p>
                • JSON content will be validated in real-time to ensure proper
                Playwright format
              </p>
              <p>
                • Duplicate filenames will be automatically renamed with
                timestamps
              </p>
            </div>
          }
          type="info"
          showIcon
        />
      </Card>

      {/* JSON Paste Modal */}
      <Modal
        title="Paste JSON Content"
        open={jsonModalVisible}
        onCancel={() => {
          setJsonModalVisible(false);
          resetJSONForm();
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setJsonModalVisible(false);
              resetJSONForm();
            }}
          >
            Cancel
          </Button>,
          <Button
            key="upload"
            type="primary"
            loading={jsonUploading}
            disabled={!jsonValidation?.isValid}
            onClick={handleJSONUpload}
          >
            Upload JSON
          </Button>,
        ]}
        width={800}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              File Name (Optional)
            </label>
            <Input
              placeholder="Enter filename (without .json extension)"
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              suffix={<span className="text-gray-400">.json</span>}
            />
            <Text type="secondary" className="text-xs">
              If not provided, a timestamp-based filename will be generated
            </Text>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              JSON Content *
            </label>
            <TextArea
              placeholder="Paste your Playwright test result JSON here..."
              value={jsonContent}
              onChange={(e) => handleJSONContentChange(e.target.value)}
              rows={12}
              className="font-mono text-sm"
            />
          </div>

          {/* JSON Validation Status */}
          {jsonValidation && (
            <div className="flex items-center space-x-2">
              {jsonValidation.isValid ? (
                <>
                  <CheckCircleOutlined className="text-green-500" />
                  <Text type="success">Valid Playwright JSON format</Text>
                </>
              ) : (
                <>
                  <CloseCircleOutlined className="text-red-500" />
                  <Text type="danger">{jsonValidation.error}</Text>
                </>
              )}
            </div>
          )}

          {jsonContent && !jsonValidation && (
            <div className="flex items-center space-x-2">
              <Text type="secondary">Paste JSON content to validate</Text>
            </div>
          )}
        </div>
      </Modal>
    </motion.div>
  );
};

export default FileManagement;
