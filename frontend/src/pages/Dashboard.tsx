import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, Col, Card, Statistic, Spin, Alert, Button } from "antd";
import { motion } from "framer-motion";
import { useNavigate } from "react-router-dom";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  TrophyOutlined,
  ExclamationCircleOutlined,
  MinusCircleOutlined,
} from "@ant-design/icons";
import toast from "react-hot-toast";

// Components
import FilterPanel from "@/components/filters/FilterPanel";
import TrendChart from "@/components/charts/TrendChart";
import StatusDistributionChart from "@/components/charts/StatusDistributionChart";
import ProjectBreakdownChart from "@/components/charts/ProjectBreakdownChart";
import TopFailingTests from "@/components/widgets/TopFailingTests";
import SlowestTests from "@/components/widgets/SlowestTests";
import ErrorCategoriesWidget from "@/components/widgets/ErrorCategoriesWidget";

// Services & Types
import apiService from "@/services/api";
import { DashboardMetrics, FilterCriteria } from "@/types";

const Dashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterCriteria>({});
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const navigate = useNavigate();

  const loadDashboardData = useCallback(
    async (showSuccessToast: boolean = false) => {
      try {
        setLoading(true);
        setError(null);

        // For dashboard metrics, we want all tests regardless of status filters
        // Status filters are only applied for display purposes in getFilteredMetrics()
        const dashboardFilters = { ...filters };
        delete dashboardFilters.status; // Remove status filter for dashboard metrics

        const data = await apiService.getDashboardMetrics(dashboardFilters);
        setMetrics(data);
        setLastUpdated(new Date());

        if (showSuccessToast) {
          toast.success("Dashboard data updated successfully");
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to load dashboard data";
        setError(errorMessage);
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    [filters]
  );

  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  const handleFilterChange = (newFilters: FilterCriteria) => {
    setFilters(newFilters);
  };

  const handleRefresh = () => {
    loadDashboardData(true); // Show toast when explicitly refreshing
  };

  // Helper function to calculate filtered metrics based on status filter
  const getFilteredMetrics = () => {
    if (!metrics) return null;

    const hasStatusFilter = filters.status && filters.status.length > 0;

    if (!hasStatusFilter) {
      // No status filter, return all metrics
      return metrics;
    }

    // Calculate filtered metrics based on selected status
    const filteredMetrics = { ...metrics };
    const selectedStatuses = filters.status || [];

    // Calculate total tests based on selected statuses
    let filteredTotal = 0;
    const filteredTestsByStatus = {
      passed: selectedStatuses.includes("passed")
        ? metrics.testsByStatus?.passed || 0
        : 0,
      failed: selectedStatuses.includes("failed")
        ? metrics.testsByStatus?.failed || 0
        : 0,
      flaky: selectedStatuses.includes("flaky")
        ? metrics.testsByStatus?.flaky || 0
        : 0,
      skipped: selectedStatuses.includes("skipped")
        ? metrics.testsByStatus?.skipped || 0
        : 0,
    };

    // Sum up the filtered totals
    filteredTotal =
      filteredTestsByStatus.passed +
      filteredTestsByStatus.failed +
      filteredTestsByStatus.flaky +
      filteredTestsByStatus.skipped;

    return {
      ...filteredMetrics,
      totalTests: filteredTotal,
      passedTests: filteredTestsByStatus.passed,
      failedTests: filteredTestsByStatus.failed,
      skippedTests: filteredTestsByStatus.skipped,
      testsByStatus: {
        ...metrics.testsByStatus,
        passed: filteredTestsByStatus.passed,
        failed: filteredTestsByStatus.failed,
        flaky: filteredTestsByStatus.flaky,
        skipped: filteredTestsByStatus.skipped,
      },
    };
  };

  const displayMetrics = getFilteredMetrics();

  // Navigation handlers for metric cards
  const handleNavigateToAnalytics = (status?: string) => {
    const searchParams = new URLSearchParams();

    // Include all current filters from dashboard
    if (filters.startDate) {
      searchParams.set("startDate", filters.startDate);
    }
    if (filters.endDate) {
      searchParams.set("endDate", filters.endDate);
    }
    if (filters.projectName && filters.projectName.length > 0) {
      filters.projectName.forEach((project) => {
        searchParams.append("projectName", project);
      });
    }
    if (filters.filePath && filters.filePath.length > 0) {
      filters.filePath.forEach((path) => {
        searchParams.append("filePath", path);
      });
    }
    if (filters.tags && filters.tags.length > 0) {
      filters.tags.forEach((tag) => {
        searchParams.append("tags", tag);
      });
    }

    // Add or override status filter if provided
    if (status) {
      searchParams.set("status", status);
    } else if (filters.status && filters.status.length > 0) {
      // If no specific status is provided, include existing status filters
      filters.status.forEach((statusItem) => {
        searchParams.append("status", statusItem);
      });
    }

    const url = `/analytics${
      searchParams.toString() ? `?${searchParams.toString()}` : ""
    }`;
    navigate(url);
  };

  if (loading && !metrics) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <Spin size="large" />
      </div>
    );
  }

  if (error && !metrics) {
    return (
      <Alert
        message="Error Loading Dashboard"
        description={error}
        type="error"
        showIcon
        action={
          <Button onClick={handleRefresh} type="primary">
            Retry
          </Button>
        }
      />
    );
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Filters */}
      <motion.div variants={itemVariants}>
        <FilterPanel
          key="dashboard-filters" // Force fresh instance for dashboard
          onFilterChange={handleFilterChange}
          onRefresh={handleRefresh}
          refreshLoading={loading}
          initialFilters={filters}
        />
      </motion.div>

      {/* Key Metrics */}
      <motion.div variants={itemVariants}>
        <Row gutter={[16, 10]}>
          <Col xs={24} sm={12} lg={4} xl={4}>
            <Card
              className="metric-card metric-card-total cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleNavigateToAnalytics()}
            >
              <Statistic
                title={
                  <span style={{ color: "white", opacity: 0.9 }}>
                    Total Tests
                  </span>
                }
                value={displayMetrics?.totalTests || 0}
                prefix={<TrophyOutlined style={{ color: "white" }} />}
                valueStyle={{ color: "white", fontWeight: "bold" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={4} xl={4}>
            <Card
              className="metric-card metric-card-pass cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleNavigateToAnalytics("passed")}
            >
              <Statistic
                title={
                  <span style={{ color: "white", opacity: 0.9 }}>
                    Passed Tests
                  </span>
                }
                value={displayMetrics?.testsByStatus?.passed || 0}
                prefix={<CheckCircleOutlined style={{ color: "white" }} />}
                valueStyle={{ color: "white", fontWeight: "bold" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={4} xl={4}>
            <Card
              className="metric-card metric-card-fail cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleNavigateToAnalytics("failed")}
            >
              <Statistic
                title={
                  <span style={{ color: "white", opacity: 0.9 }}>
                    Failed Tests
                  </span>
                }
                value={displayMetrics?.testsByStatus?.failed || 0}
                prefix={<CloseCircleOutlined style={{ color: "white" }} />}
                valueStyle={{ color: "white", fontWeight: "bold" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={4} xl={4}>
            <Card
              className="metric-card metric-card-flaky cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleNavigateToAnalytics("flaky")}
            >
              <Statistic
                title={
                  <span style={{ color: "white", opacity: 0.9 }}>
                    Flaky Tests
                  </span>
                }
                value={displayMetrics?.testsByStatus?.flaky || 0}
                prefix={
                  <ExclamationCircleOutlined style={{ color: "white" }} />
                }
                valueStyle={{ color: "white", fontWeight: "bold" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={4} xl={4}>
            <Card
              className="metric-card metric-card-skipped cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleNavigateToAnalytics("skipped")}
            >
              <Statistic
                title={
                  <span style={{ color: "white", opacity: 0.9 }}>
                    Skipped Tests
                  </span>
                }
                value={displayMetrics?.testsByStatus?.skipped || 0}
                prefix={<MinusCircleOutlined style={{ color: "white" }} />}
                valueStyle={{ color: "white", fontWeight: "bold" }}
              />
            </Card>
          </Col>

          <Col xs={24} sm={12} lg={4} xl={4}>
            <Card className="metric-card metric-card-duration">
              <Statistic
                title={
                  <span style={{ color: "white", opacity: 0.9 }}>
                    Avg Duration
                  </span>
                }
                value={(displayMetrics?.averageDuration || 0) / 1000}
                precision={1}
                suffix="s"
                prefix={<ClockCircleOutlined style={{ color: "white" }} />}
                valueStyle={{ color: "white", fontWeight: "bold" }}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>
      <br />
      {/* Charts Row 1 */}
      <motion.div variants={itemVariants}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Card title="Pass/Fail Trend" className="h-96">
              <TrendChart
                data={displayMetrics?.trendData || []}
                loading={loading}
              />
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title="Test Status Distribution" className="h-96">
              <StatusDistributionChart
                data={{
                  passed: displayMetrics?.testsByStatus?.passed || 0,
                  failed: displayMetrics?.testsByStatus?.failed || 0,
                  skipped: displayMetrics?.testsByStatus?.skipped || 0,
                  flaky: displayMetrics?.testsByStatus?.flaky || 0,
                }}
                loading={loading}
              />
            </Card>
          </Col>
        </Row>
      </motion.div>
      <br />
      {/* Charts Row 2 */}
      <motion.div variants={itemVariants}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={12}>
            <Card title="Tests by Project" className="h-80">
              <ProjectBreakdownChart
                data={displayMetrics?.testsByProject || {}}
                loading={loading}
              />
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <ErrorCategoriesWidget filters={filters} loading={loading} />
          </Col>
        </Row>
      </motion.div>
      <br />
      {/* Bottom Widgets */}
      <motion.div variants={itemVariants}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={12}>
            <TopFailingTests
              tests={displayMetrics?.topFailingTests || []}
              loading={loading}
            />
          </Col>

          <Col xs={24} lg={12}>
            <SlowestTests
              tests={displayMetrics?.slowestTests || []}
              loading={loading}
            />
          </Col>
        </Row>
      </motion.div>

      {/* Footer - Last Updated */}
      <motion.div variants={itemVariants}>
        <div className="text-center py-4 mt-8 border-t border-gray-200">
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated.toLocaleString()}
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default Dashboard;
