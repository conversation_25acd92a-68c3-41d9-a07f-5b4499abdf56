{"name": "cbi-e2e-analytics-frontend", "version": "1.0.0", "description": "CBI-E2E Automation Analytics Dashboard", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix"}, "dependencies": {"@ant-design/icons": "^5.2.6", "antd": "^5.12.0", "axios": "^1.6.0", "chart.js": "^4.4.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "framer-motion": "^10.16.0", "lodash": "^4.17.21", "plotly.js": "^2.27.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.21.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-grid-layout": "^1.4.4", "react-hot-toast": "^2.4.1", "react-plotly.js": "^2.6.0", "react-router-dom": "^6.8.1", "react-select": "^5.8.0", "react-table": "^7.8.0", "react-vis": "^1.12.1", "recharts": "^2.8.0", "styled-components": "^6.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/lodash": "^4.14.202", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^4.5.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}