<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>CBI-E2E Automation Analytics</title>
    <meta name="description" content="Comprehensive analytics dashboard for Playwright E2E test results" />
    <meta name="keywords" content="playwright, testing, analytics, dashboard, e2e, automation" />
    <meta name="author" content="CBI Team" />
    
    <!-- Preload critical resources -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#1890ff" />
    
    <!-- Open Graph meta tags -->
    <meta property="og:title" content="CBI-E2E Automation Analytics" />
    <meta property="og:description" content="Comprehensive analytics dashboard for Playwright E2E test results" />
    <meta property="og:type" content="website" />
    
    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="CBI-E2E Automation Analytics" />
    <meta name="twitter:description" content="Comprehensive analytics dashboard for Playwright E2E test results" />
    
    <style>
      /* Critical CSS for initial load */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #f5f5f5;
        color: #333;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* Loading spinner */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid #ffffff30;
        border-top: 3px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        margin-top: 20px;
        font-size: 18px;
        font-weight: 500;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Initial loading state -->
      <div class="loading-container">
        <div>
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading CBI-E2E Analytics...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
