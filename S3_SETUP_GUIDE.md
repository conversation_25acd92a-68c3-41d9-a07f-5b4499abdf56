# S3 Integration Setup Guide

## Overview

This guide will help you configure S3 integration to automatically pick up videos and screenshots from your AWS bucket and attach them to test reports.

## Your Bucket Structure

Based on your provided structure:

```
dev/
ourl-lemon/
2025-07-13T18:30:00Z-pw-originalurl-nightly-1752431400-e2e-test-1686646514/
playwright-report/
data/
```

## Step 1: Configure Environment Variables

1. **Update the backend/.env file** with your actual AWS credentials:

```bash
# S3 Integration Configuration
ENABLE_S3_INTEGRATION=true

# AWS Configuration - REPLACE WITH YOUR ACTUAL VALUES
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=YOUR_ACTUAL_ACCESS_KEY_HERE
AWS_SECRET_ACCESS_KEY=YOUR_ACTUAL_SECRET_KEY_HERE
AWS_S3_BUCKET=YOUR_ACTUAL_BUCKET_NAME_HERE

# S3 Path Prefixes - Based on your bucket structure
# JSON files are in playwright-report/json-results/ subdirectories
# Videos and screenshots are in playwright-report/data/ subdirectories
AWS_S3_DATA_PREFIX=workflowtest/dev/ourl-lemon/
AWS_S3_VIDEO_PREFIX=workflowtest/dev/ourl-lemon/
AWS_S3_SCREENSHOT_PREFIX=workflowtest/dev/ourl-lemon/

# Sync Configuration
S3_SYNC_INTERVAL_MINUTES=5
```

2. **Replace the placeholder values**:
   - `YOUR_ACTUAL_ACCESS_KEY_HERE` → Your AWS Access Key ID
   - `YOUR_ACTUAL_SECRET_KEY_HERE` → Your AWS Secret Access Key
   - `YOUR_ACTUAL_BUCKET_NAME_HERE` → Your S3 bucket name

## Step 2: How the System Works

### Automatic File Discovery

The system will:

1. **Scan for JSON files** in `dev/ourl-lemon/*/playwright-report/json-results/` directories
2. **Process test results** from these JSON files
3. **Look for videos and screenshots** in the corresponding `playwright-report/data/` directories
4. **Generate presigned URLs** for media files when requested

### Expected File Structure in S3

```
your-bucket/
├── workflowtest/
│   └── dev/
│       └── ourl-lemon/
│           └── 2025-07-13T18:30:00Z-pw-originalurl-nightly-1752431400-e2e-test-1686646514/
│               └── playwright-report/
│                   ├── json-results/
│                   │   └── test-results.json                              ← Test data files
│                   └── data/
│                       ├── 0054d4ea29a2947049dd703035c7134e978a5f64.webm  ← Test execution videos (hash-based names)
│                       ├── screenshot1.png                               ← Test screenshots
│                       └── screenshot2.png                               ← Test screenshots
```

### Video and Screenshot Mapping

The system will automatically:

- **Scan the last 3 run folders** for new test results
- **Find JSON files** in `playwright-report/json-results/` directories
- **Map videos** to `workflowtest/dev/ourl-lemon/{timestamp-folder}/playwright-report/data/{hash}.webm`
- **Map screenshots** to `workflowtest/dev/ourl-lemon/{timestamp-folder}/playwright-report/data/{screenshot-filename}.png`

## Step 3: Start the System

1. **Restart the backend** to apply the new configuration:

```bash
cd backend
go run main.go
```

2. **Check S3 status** in the frontend:
   - Navigate to the S3 Configuration page
   - Verify connection status shows "Connected"
   - Check that the bucket name is displayed correctly

## Step 4: Trigger Initial Sync

1. **Manual sync** via the frontend:

   - Go to S3 Configuration page
   - Click "Trigger Sync" button
   - Monitor the logs for processing status

2. **Automatic sync** will run every 5 minutes (configurable)

## Step 5: Verify Integration

1. **Check test cases page**:

   - Navigate to Test Cases page
   - Look for "Play Video" buttons (should be enabled for tests with videos)
   - Check screenshot galleries for tests with screenshots

2. **Monitor logs** for any processing errors:

```bash
# Backend logs will show:
# - S3 file scan results
# - Processing status for each file
# - Any errors encountered
```

## Troubleshooting

### Common Issues

1. **"S3 integration disabled"**:

   - Verify `ENABLE_S3_INTEGRATION=true` in .env
   - Check AWS credentials are correct
   - Restart the backend

2. **"No new files found"**:

   - Verify JSON files exist in `playwright-report/json-results/` directories
   - Check the `AWS_S3_DATA_PREFIX` matches your bucket structure
   - Use "Clear Cache" button to reset processed files

3. **Videos/Screenshots not loading**:
   - Check file paths in the JSON test results
   - Verify files exist in the expected S3 locations
   - Check AWS permissions for the bucket

### AWS Permissions Required

Your AWS credentials need the following S3 permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["s3:GetObject", "s3:ListBucket", "s3:HeadObject"],
      "Resource": [
        "arn:aws:s3:::your-bucket-name",
        "arn:aws:s3:::your-bucket-name/*"
      ]
    }
  ]
}
```

## Next Steps

Once configured, the system will:

- ✅ Automatically discover new test result files
- ✅ Process and display test data in the dashboard
- ✅ Provide video playback for test executions
- ✅ Show screenshot galleries for visual test verification
- ✅ Generate presigned URLs for secure media access

The integration runs continuously, checking for new files every 5 minutes and processing them automatically.
