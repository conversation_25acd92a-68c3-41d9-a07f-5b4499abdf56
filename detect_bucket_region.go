package main

import (
	"fmt"
	"log"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

func main() {
	// Your credentials
	accessKey := "ASIARKYN4QXV3JMO3TQ7"
	secretKey := "d6wiB/Rgo5I5JeHKYGiFCuPvBsgH4CsYyzjvk7ok"
	bucket := "appsulate-jenkins-reports"

	// Common AWS regions to try
	regions := []string{
		"us-east-1",
		"us-west-1", 
		"us-west-2",
		"eu-west-1",
		"eu-central-1",
		"ap-southeast-1",
		"ap-northeast-1",
	}

	fmt.Printf("Detecting region for bucket: %s\n", bucket)

	for _, region := range regions {
		fmt.Printf("Trying region: %s... ", region)
		
		// Create session for this region
		sess, err := session.NewSession(&aws.Config{
			Region:      aws.String(region),
			Credentials: credentials.NewStaticCredentials(accessKey, secretKey, ""),
		})
		if err != nil {
			fmt.Printf("Failed to create session: %v\n", err)
			continue
		}

		// Create S3 service client
		svc := s3.New(sess)

		// Try to get bucket location
		input := &s3.GetBucketLocationInput{
			Bucket: aws.String(bucket),
		}

		result, err := svc.GetBucketLocation(input)
		if err != nil {
			fmt.Printf("Error: %v\n", err)
			continue
		}

		// Handle the result
		var bucketRegion string
		if result.LocationConstraint == nil {
			bucketRegion = "us-east-1" // Default region
		} else {
			bucketRegion = *result.LocationConstraint
		}

		fmt.Printf("SUCCESS! Bucket is in region: %s\n", bucketRegion)
		
		// Test listing objects to confirm
		fmt.Printf("Testing object listing in region %s...\n", bucketRegion)
		listInput := &s3.ListObjectsV2Input{
			Bucket:  aws.String(bucket),
			Prefix:  aws.String("workflowtest/dev/ourl-lemon/"),
			MaxKeys: aws.Int64(5),
		}

		listResult, err := svc.ListObjectsV2(listInput)
		if err != nil {
			fmt.Printf("Failed to list objects: %v\n", err)
		} else {
			fmt.Printf("Successfully listed %d objects\n", len(listResult.Contents))
			for i, obj := range listResult.Contents {
				fmt.Printf("  %d. %s\n", i+1, *obj.Key)
			}
		}

		fmt.Printf("\n✅ Update your .env file with: AWS_REGION=%s\n", bucketRegion)
		return
	}

	fmt.Println("❌ Could not detect bucket region. Please check your credentials and bucket name.")
}
