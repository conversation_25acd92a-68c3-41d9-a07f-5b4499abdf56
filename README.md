# CBI-E2E Automation Analytics

A comprehensive analytics dashboard for Playwright E2E test results with real-time insights, advanced visualizations, and intuitive file management.

## 🎭 Features

### 📊 **Analytics Dashboard**

- **Real-time Metrics**: Pass rates, fail rates, test counts, and duration analytics
- **Trend Analysis**: Historical performance tracking with interactive charts
- **Test Breakdown**: Detailed analysis by project, priority, and status
- **Performance Insights**: Identify slowest tests and most failing scenarios

### 📈 **Advanced Visualizations**

- **Line Charts**: Pass/fail rate trends over time
- **Doughnut Charts**: Test status distribution
- **Bar Charts**: Project breakdown and duration analysis
- **Bubble Charts**: Test complexity vs success rate correlation
- **Heatmaps**: Performance matrix visualization
- **Sunburst Charts**: Hierarchical test structure
- **Word Clouds**: Error message analysis

### 🗂️ **File Management**

- **Drag & Drop Upload**: Easy JSON file uploads
- **Batch Processing**: Multiple file upload support
- **File Validation**: Automatic Playwright format validation
- **Storage Management**: View, delete, and organize test result files

### ☁️ **S3 Integration** _(Optional)_

- **Video Storage**: Store and display test execution videos from S3
- **Screenshot Gallery**: View test screenshots with thumbnail previews
- **Automated Data Ingestion**: Automatically process test results from S3 buckets
- **Presigned URLs**: Secure, time-limited access to video content
- **Real-time Sync**: Configurable sync intervals for new test data

### 🔍 **Smart Filtering**

- **Date Range**: Filter by test execution dates
- **Status Filter**: Filter by passed/failed/skipped tests
- **Project Filter**: Analyze specific project results
- **Priority Filter**: Focus on P1, P2, P3 test priorities
- **Tag Filter**: Filter by test tags and annotations

## 🏗️ **Architecture**

### **Backend (Go)**

- **Domain-Driven Design**: Clean architecture with separated concerns
- **RESTful API**: Comprehensive endpoints for analytics and file management
- **JSON Processing**: Efficient Playwright test result parsing
- **CORS Support**: Seamless frontend-backend integration

### **Frontend (React + TypeScript)**

- **Modern Stack**: React 18, TypeScript, Vite
- **UI Framework**: Ant Design with custom styling
- **Charts**: Chart.js and Recharts for visualizations
- **State Management**: React hooks and context
- **Responsive Design**: Mobile-first approach

## 🚀 **Quick Start**

### **Prerequisites**

- Go 1.21+
- Node.js 18+
- npm or yarn

### **Backend Setup**

```bash
cd backend
go mod tidy
go run main.go
```

The API will be available at `http://localhost:8080`

### **Frontend Setup**

```bash
cd frontend
npm install
npm run dev
```

The dashboard will be available at `http://localhost:3000`

## 📁 **Project Structure**

```
CBI-E2E-Analytics/
├── backend/
│   ├── domain/
│   │   ├── entities/          # Core business entities
│   │   ├── services/          # Domain services
│   │   └── valueobjects/      # Value objects
│   ├── application/
│   │   └── usecases/          # Application use cases
│   ├── infrastructure/
│   │   ├── api/               # HTTP handlers and routes
│   │   └── repositories/      # Data access layer
│   ├── json-test-results/     # Test result storage
│   ├── go.mod
│   └── main.go
├── frontend/
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   ├── pages/             # Page components
│   │   ├── services/          # API services
│   │   ├── types/             # TypeScript definitions
│   │   └── utils/             # Utility functions
│   ├── package.json
│   └── vite.config.ts
└── README.md
```

## 🔌 **API Endpoints**

### **Analytics**

- `GET /api/analytics/dashboard` - Dashboard metrics
- `GET /api/analytics/tests` - Detailed test analytics
- `GET /api/analytics/charts/:type` - Chart data
- `GET /api/analytics/filter-options` - Available filter options

### **File Management**

- `GET /api/files` - List uploaded files
- `POST /api/files/upload` - Upload single file
- `POST /api/files/upload-multiple` - Upload multiple files
- `DELETE /api/files/:filename` - Delete file

### **S3 Integration** _(When Enabled)_

- `GET /api/v1/videos/:s3Key` - Get presigned video URL
- `GET /api/v1/videos/status` - S3 connection status
- `GET /api/v1/s3/status` - S3 integration status
- `POST /api/v1/s3/sync` - Trigger manual S3 sync
- `POST /api/v1/s3/clear-cache` - Clear processed files cache

### **Health Check**

- `GET /api/health` - Service health status

## 📊 **Supported Metrics**

### **Test Analytics Table**

- Serial Number
- Test Name & File Path
- Pass Rate (%) with visual indicators
- Fail Rate (%)
- Total Runs
- Average Duration
- Retry Count
- Project Name
- Priority Level (P1, P2, P3)
- Last Status
- Tags

### **Dashboard Metrics**

- Total Tests Count
- Overall Pass Rate
- Failed Tests Count
- Average Test Duration
- Tests by Project Distribution
- Test Status Distribution
- Top Failing Tests
- Slowest Tests
- Most Retried Tests

## 🎨 **Visualization Types**

1. **Trend Charts**: Pass/fail rates over time
2. **Status Distribution**: Doughnut chart of test statuses
3. **Project Breakdown**: Bar chart of tests by project
4. **Duration Analysis**: Test execution time analysis
5. **Error Categories**: Pie chart of error types
6. **Performance Heatmap**: Test performance matrix
7. **Bubble Charts**: Complexity vs success correlation
8. **Sunburst Charts**: Hierarchical test visualization
9. **Word Clouds**: Error message frequency analysis

## 🔧 **Configuration**

### **Environment Variables**

- `PORT`: Backend server port (default: 8080)
- `DATA_PATH`: Custom path for JSON files storage
- `GIN_MODE`: Gin framework mode (debug/release)

### **Frontend Configuration**

- `VITE_API_BASE_URL`: Backend API URL (default: http://localhost:8080/api)

## 📝 **Usage**

1. **Upload Test Results**: Drag and drop Playwright JSON files
2. **Apply Filters**: Use date range, status, project, and priority filters
3. **Analyze Trends**: View pass/fail trends and performance metrics
4. **Identify Issues**: Find failing tests and performance bottlenecks
5. **Export Insights**: Use the data for reporting and decision making

## 🛠️ **Development**

### **Adding New Visualizations**

1. Create chart component in `frontend/src/components/charts/`
2. Add API endpoint in `backend/infrastructure/api/handlers/`
3. Update types in `frontend/src/types/index.ts`
4. Integrate in dashboard or trends page

### **Extending Analytics**

1. Add new metrics in `backend/domain/valueobjects/analytics.go`
2. Update calculation logic in `backend/domain/services/analytics_service.go`
3. Create corresponding frontend components

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📚 **Additional Documentation**

- **[S3 Integration Guide](S3_INTEGRATION.md)**: Complete setup guide for S3 video storage and data ingestion
- **[API Documentation](API.md)**: Detailed API reference (if available)

## 📄 **License**

This project is licensed under the MIT License.

## 🙏 **Acknowledgments**

- **Playwright Team**: For the excellent testing framework
- **Ant Design**: For the beautiful UI components
- **Chart.js**: For powerful charting capabilities
- **Go Gin**: For the fast HTTP framework

---

**Built with ❤️ for the QA Community**

_Empowering teams with actionable test insights and beautiful analytics._
