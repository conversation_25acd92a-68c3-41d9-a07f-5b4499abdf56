#!/bin/bash

# Docker entrypoint script for CBI-E2E Analytics Platform
# Runs both frontend and backend services in a single container

set -e

# Colors for logging
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to handle shutdown gracefully
cleanup() {
    log_info "Shutting down services..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        log_info "Stopping backend (PID: $BACKEND_PID)"
        kill -TERM "$BACKEND_PID" 2>/dev/null || true
        wait "$BACKEND_PID" 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        log_info "Stopping frontend (PID: $FRONTEND_PID)"
        kill -TERM "$FRONTEND_PID" 2>/dev/null || true
        wait "$FRONTEND_PID" 2>/dev/null || true
    fi
    
    log_info "Services stopped gracefully"
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Print startup banner
echo "
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║              CBI-E2E Analytics Platform                       ║
║                                                               ║
║              🐳 Docker Container Starting...                 ║
║              🎭 Playwright Test Results Analytics            ║
║              📊 Real-time Dashboard & Insights               ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
"

# Validate environment
log_info "Validating environment..."

# Check if backend binary exists
if [ ! -f "./backend/main" ]; then
    log_error "Backend binary not found at ./backend/main"
    exit 1
fi

# Check if frontend dist exists
if [ ! -d "./frontend/dist" ]; then
    log_error "Frontend dist directory not found at ./frontend/dist"
    exit 1
fi

# Create data directory if it doesn't exist
mkdir -p "${DATA_PATH:-/app/backend/json-test-results}"

# Set default environment variables
export GIN_MODE="${GIN_MODE:-release}"
export PORT="${PORT:-8080}"
export DATA_PATH="${DATA_PATH:-/app/backend/json-test-results}"

log_info "Environment configured:"
log_info "  - GIN_MODE: $GIN_MODE"
log_info "  - PORT: $PORT"
log_info "  - DATA_PATH: $DATA_PATH"

# Start backend service
log_info "Starting backend service..."
cd /app/backend
./main &
BACKEND_PID=$!
cd /app

log_info "Backend started with PID: $BACKEND_PID"

# Wait a moment for backend to start
sleep 2

# Check if backend is running
if ! kill -0 "$BACKEND_PID" 2>/dev/null; then
    log_error "Backend failed to start"
    exit 1
fi

# Start frontend server using Python (most reliable approach)
log_info "Starting frontend static file server..."

# Ensure Python3 is available
if ! command -v python3 >/dev/null 2>&1; then
    log_info "Installing Python3..."
    apk add --no-cache python3 >/dev/null 2>&1
fi

# Create a simple Python server that serves static files and proxies API calls
cat > serve_frontend.py << 'EOF'
#!/usr/bin/env python3
import http.server
import socketserver
import os
import urllib.request
import urllib.parse
import json
from urllib.error import URLError, HTTPError

class ProxyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve static files from
        super().__init__(*args, directory='/app/frontend/dist', **kwargs)

    def do_GET(self):
        if self.path.startswith('/api/'):
            self.proxy_to_backend()
        else:
            # For SPA routing, serve index.html for non-existent files
            if self.path != '/' and not os.path.exists(f"/app/frontend/dist{self.path}"):
                self.path = '/index.html'
            super().do_GET()

    def do_POST(self):
        if self.path.startswith('/api/'):
            self.proxy_to_backend()
        else:
            self.send_error(404, "Not Found")

    def do_PUT(self):
        if self.path.startswith('/api/'):
            self.proxy_to_backend()
        else:
            self.send_error(404, "Not Found")

    def do_DELETE(self):
        if self.path.startswith('/api/'):
            self.proxy_to_backend()
        else:
            self.send_error(404, "Not Found")

    def proxy_to_backend(self):
        try:
            # Build backend URL
            backend_url = f"http://localhost:8080{self.path}"
            if self.command == 'GET' and '?' in self.path:
                # Query parameters are already in self.path
                pass

            # Read request body for POST/PUT requests
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = None
            if content_length > 0:
                post_data = self.rfile.read(content_length)

            # Create request
            req = urllib.request.Request(backend_url, data=post_data, method=self.command)

            # Copy relevant headers
            for header, value in self.headers.items():
                if header.lower() not in ['host', 'connection', 'content-length']:
                    req.add_header(header, value)

            # Make request to backend
            try:
                with urllib.request.urlopen(req, timeout=30) as response:
                    # Send response status
                    self.send_response(response.getcode())

                    # Copy response headers
                    for header, value in response.headers.items():
                        if header.lower() not in ['connection', 'transfer-encoding']:
                            self.send_header(header, value)
                    self.end_headers()

                    # Copy response body
                    self.wfile.write(response.read())
            except HTTPError as e:
                self.send_response(e.code)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                error_response = json.dumps({"error": f"Backend error: {e.reason}"})
                self.wfile.write(error_response.encode())

        except URLError as e:
            self.send_error(502, f"Backend unavailable: {e.reason}")
        except Exception as e:
            self.send_error(500, f"Proxy error: {str(e)}")

    def log_message(self, format, *args):
        # Custom logging format
        print(f"[{self.log_date_time_string()}] {format % args}")

# Start the server
PORT = 3000
print(f"Starting frontend server on port {PORT}")
print(f"Serving static files from: /app/frontend/dist")
print(f"Proxying API requests to: http://localhost:8080")

Handler = ProxyHTTPRequestHandler
with socketserver.TCPServer(("0.0.0.0", PORT), Handler) as httpd:
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down frontend server...")
        httpd.shutdown()
EOF

# Start the Python server
python3 serve_frontend.py &
FRONTEND_PID=$!

log_info "Frontend server started with PID: $FRONTEND_PID"

# Print service information
log_info "Services started successfully!"
log_info "  🎯 Frontend: http://localhost:3000"
log_info "  🔧 Backend API: http://localhost:8080/api"
log_info "  📊 Dashboard: http://localhost:3000"
log_info "  📁 Data Path: $DATA_PATH"

# Wait for services to be ready
log_info "Waiting for services to be ready..."
sleep 5

# Health check
log_info "Performing health check..."
if curl -f http://localhost:8080/api/health >/dev/null 2>&1; then
    log_info "✅ Backend health check passed"
else
    log_warn "⚠️  Backend health check failed, but continuing..."
fi

if curl -f http://localhost:3000 >/dev/null 2>&1; then
    log_info "✅ Frontend health check passed"
else
    log_warn "⚠️  Frontend health check failed, but continuing..."
fi

log_info "🚀 CBI-E2E Analytics Platform is ready!"
log_info "   Access the dashboard at: http://localhost:3000"

# Keep the container running and wait for signals
wait
