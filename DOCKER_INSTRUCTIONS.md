# 🚀 CBI-E2E Analytics Platform - Quick Start

## 📋 Prerequisites

- Docker installed and running
- Docker Compose installed
- AWS credentials with S3 access

## 🏗️ Setup

### Step 1: Pull Docker Image

```bash
# Pull the image from Docker Hub
docker pull 091834058219.dkr.ecr.eu-central-1.amazonaws.com/cbi-e2e-analytics-v1.0.0:latest
```

### Step 2: Prepare Environment

```bash
# Copy the template to create your .env file
cp backend/.env.template backend/.env

# Edit backend/.env and add your AWS credentials (optional - you can pass them as parameters instead)
```

## 🚀 Running the Application

### Option 1: Pass AWS credentials as parameters (Recommended)

```bash
# With Access Key and Secret Key
./run-docker-with-aws.sh YOUR_ACCESS_KEY YOUR_SECRET_KEY

# With Access Key, Secret Key, and Session Token (for temporary credentials)
./run-docker-with-aws.sh YOUR_ACCESS_KEY YOUR_SECRET_KEY YOUR_SESSION_TOKEN
```

### Option 2: Set environment variables

```bash
# Set credentials
export AWS_ACCESS_KEY_ID=your_access_key
export AWS_SECRET_ACCESS_KEY=your_secret_key
export AWS_SESSION_TOKEN=your_session_token  # Optional

# Run
docker-compose up -d
```

### Option 3: Edit .env file and run

```bash
# Edit backend/.env file and add your AWS credentials
# Then run:
./run-docker-with-aws.sh
```

## 🌐 Access the Application

Once running, access:

- **Frontend Dashboard**: http://localhost:3000
- **Backend API**: http://localhost:8080/api
- **S3 Configuration**: http://localhost:3000/s3-config

## 🔧 Management Commands

```bash
# View logs
docker-compose logs -f

# Stop the application
docker-compose down

# Restart
docker-compose restart

# Check status
docker-compose ps
```

## 🔍 Troubleshooting

### Check if S3 is working:

```bash
# Test S3 connection
curl http://localhost:8080/api/v1/s3/status

# Check container logs
docker-compose logs | grep -i s3
```

### Common Issues:

1. **Port conflicts**: Make sure ports 3000 and 8080 are free
2. **AWS credentials**: Ensure your credentials have S3 access permissions
3. **Docker not running**: Make sure Docker daemon is running

## 📞 Support

If you encounter issues, share the output of:

```bash
docker-compose logs
```
