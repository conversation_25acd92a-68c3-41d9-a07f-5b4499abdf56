{"config": {"configFile": "/path/to/playwright.config.js", "rootDir": "/path/to/project", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": {}, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["json", {"outputFile": "test-results.json"}]], "reportSlowTests": {"max": 5, "threshold": 15000}, "quiet": false, "projects": [{"outputDir": "/path/to/test-results", "repeatEach": 1, "retries": 0, "name": "chromium", "testDir": "/path/to/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "version": "1.40.0", "workers": 4, "webServer": null}, "suites": [{"title": "", "file": "", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Sample Test Suite", "file": "/path/to/tests/sample.spec.js", "line": 1, "column": 0, "specs": [{"title": "should pass basic test", "ok": true, "tags": ["smoke", "regression"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "status": "passed", "duration": 1234, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T14:59:02.866Z", "attachments": []}], "status": "expected"}], "id": "sample-test-1", "file": "/path/to/tests/sample.spec.js", "line": 3, "column": 2}, {"title": "should handle failure case", "ok": false, "tags": ["regression"], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "status": "failed", "duration": 2345, "errors": [{"message": "Test failed: Expected element to be visible", "stack": "Error: Test failed\n    at test (/path/to/tests/sample.spec.js:15:5)"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T14:59:05.000Z", "attachments": []}], "status": "unexpected"}], "id": "sample-test-2", "file": "/path/to/tests/sample.spec.js", "line": 10, "column": 2}]}]}], "errors": [], "stats": {"startTime": "2025-05-30T14:59:02.866Z", "duration": 5000, "expected": 1, "unexpected": 1, "flaky": 0, "skipped": 0}}