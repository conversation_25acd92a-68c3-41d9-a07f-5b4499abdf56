# Multi-stage Dockerfile for CBI-E2E Analytics Platform
# Builds both React frontend and Go backend in a single lightweight image

# Stage 1: Build Frontend (React/Vite)
FROM node:20-alpine AS frontend-builder

# Install build dependencies
RUN apk add --no-cache git python3 make g++

WORKDIR /app/frontend

# Copy package files first for better Docker layer caching
COPY frontend/package.json frontend/package-lock.json ./

# Install dependencies with legacy peer deps to handle React version conflicts
RUN npm ci --legacy-peer-deps --no-audit --no-fund

# Copy all frontend source files
COPY frontend/ ./

# Build frontend for production
RUN npm run build && ls -la dist/

# Stage 2: Build Backend (Go)
FROM golang:1.22-alpine AS backend-builder

WORKDIR /app/backend

# Install git (required for go mod download)
RUN apk add --no-cache git

# Copy go mod files
COPY backend/go.mod backend/go.sum ./

# Download dependencies
RUN go mod download

# Copy backend source
COPY backend/ ./

# Build Go binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# Stage 3: Final Runtime Image
FROM alpine:3.18

# Install ca-certificates for HTTPS requests, bash for scripts, and python3 for frontend server
RUN apk --no-cache add ca-certificates bash curl python3

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Copy built backend binary
COPY --from=backend-builder /app/backend/main ./backend/

# Copy built frontend assets
COPY --from=frontend-builder /app/frontend/dist ./frontend/dist/

# Create data directory for JSON test results
RUN mkdir -p ./backend/json-test-results && \
    chown -R appuser:appgroup /app

# Copy entrypoint script
COPY docker-entrypoint.sh ./
RUN chmod +x docker-entrypoint.sh

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 8080 3000

# Environment variables
ENV GIN_MODE=release
ENV PORT=8080
ENV DATA_PATH=/app/backend/json-test-results
ENV ENABLE_S3_INTEGRATION=false

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/health || exit 1

# Start both services
ENTRYPOINT ["./docker-entrypoint.sh"]
