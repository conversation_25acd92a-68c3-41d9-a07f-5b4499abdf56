#!/bin/bash

# Helper script to run CBI-E2E Analytics Platform with AWS credentials
# Usage: ./run-docker-with-aws.sh [ACCESS_KEY] [SECRET_KEY] [SESSION_TOKEN]

set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Print banner
echo "
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║              CBI-E2E Analytics Platform                       ║
║              🐳 Docker Deployment with AWS                   ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    log_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Parse arguments
if [ $# -eq 0 ]; then
    log_info "No AWS credentials provided. Using backend/.env file settings."
    log_info "Starting with docker-compose..."
    docker-compose down 2>/dev/null || true
    docker-compose up -d
elif [ $# -eq 2 ]; then
    log_info "AWS Access Key and Secret Key provided."
    log_info "Starting with docker-compose and AWS credentials..."
    docker-compose down 2>/dev/null || true
    AWS_ACCESS_KEY_ID="$1" AWS_SECRET_ACCESS_KEY="$2" docker-compose up -d
elif [ $# -eq 3 ]; then
    log_info "AWS Access Key, Secret Key, and Session Token provided."
    log_info "Starting with docker-compose and AWS credentials..."
    docker-compose down 2>/dev/null || true
    AWS_ACCESS_KEY_ID="$1" AWS_SECRET_ACCESS_KEY="$2" AWS_SESSION_TOKEN="$3" docker-compose up -d
else
    log_error "Invalid number of arguments."
    echo ""
    echo "Usage:"
    echo "  $0                                    # Use backend/.env file"
    echo "  $0 ACCESS_KEY SECRET_KEY              # Provide AWS credentials"
    echo "  $0 ACCESS_KEY SECRET_KEY SESSION_TOKEN # Provide AWS credentials with session token"
    echo ""
    echo "Examples:"
    echo "  $0"
    echo "  $0 AKIAIOSFODNN7EXAMPLE wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
    echo "  $0 AKIAIOSFODNN7EXAMPLE wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY IQoJb3JpZ2luX2VjEF0a..."
    exit 1
fi

# Wait for services to start
log_info "Waiting for services to start..."
sleep 5

# Check if services are running
if docker-compose ps | grep -q "Up"; then
    log_info "✅ Services started successfully!"
    echo ""
    log_info "🌐 Access the application:"
    log_info "  📊 Frontend Dashboard: http://localhost:3000"
    log_info "  🔧 Backend API: http://localhost:8080/api"
    log_info "  ⚙️  S3 Configuration: http://localhost:3000/s3-config"
    echo ""
    log_info "📋 Management commands:"
    log_info "  View logs: docker-compose logs -f"
    log_info "  Stop: docker-compose down"
    log_info "  Restart: docker-compose restart"
else
    log_error "❌ Failed to start services. Check logs with: docker-compose logs"
    exit 1
fi
