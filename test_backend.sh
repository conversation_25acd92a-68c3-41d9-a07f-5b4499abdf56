#!/bin/bash

echo "Starting backend..."
cd backend

# Kill any existing process on port 8080
lsof -ti:8080 | xargs kill -9 2>/dev/null || true

# Start the backend in background
go run main.go &
BACKEND_PID=$!

# Wait a moment for the backend to start
sleep 3

echo "Testing S3 status endpoint..."
curl -s http://localhost:8080/api/v1/s3/status | jq . || echo "Failed to get S3 status"

echo "Testing health endpoint..."
curl -s http://localhost:8080/api/v1/health | jq . || echo "Failed to get health status"

# Kill the backend process
kill $BACKEND_PID 2>/dev/null || true

echo "Test completed."
