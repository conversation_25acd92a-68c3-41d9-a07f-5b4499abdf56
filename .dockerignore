# Docker ignore file for CBI-E2E Analytics Platform
# Excludes unnecessary files from Docker build context

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Frontend specific
frontend/node_modules/
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local
frontend/npm-debug.log*
frontend/yarn-debug.log*
frontend/yarn-error.log*
frontend/dist/
frontend/build/
frontend/.vite/
frontend/coverage/

# Node modules at any level
**/node_modules/
node_modules/

# Backend specific
backend/bin/
backend/tmp/
backend/*.log
backend/.env

# Test results and data (will be mounted or created at runtime)
backend/json-test-results/
*.json

# Build artifacts
*.exe
*.dll
*.so
*.dylib
*.test
*.out

# Dependency directories
vendor/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Logs
logs/
*.log

# Coverage reports
coverage/
*.cover
*.py,cover

# Docker files (don't copy into image)
Dockerfile*
docker-compose*.yml
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml
.circleci/

# Package manager lock files (copied separately for better caching)
# frontend/package-lock.json
# backend/go.sum
